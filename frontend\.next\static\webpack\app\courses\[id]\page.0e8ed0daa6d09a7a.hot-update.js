"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/courses/[id]/page",{

/***/ "(app-pages-browser)/./src/components/QnAWithHeyGenModal.tsx":
/*!***********************************************!*\
  !*** ./src/components/QnAWithHeyGenModal.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var livekit_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! livekit-client */ \"(app-pages-browser)/./node_modules/livekit-client/dist/livekit-client.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst QnAWithHeyGenModal = (param)=>{\n    let { isOpen, onClose, courseId } = param;\n    _s();\n    // Configuration\n    const API_CONFIG = {\n        apiKey: \"NDM2Y2M0NDI0YTMzNGM1OWFkZThhZTRmMjJhZmEzNzYtMTc0NzA1OTcyOA==\",\n        serverUrl: \"https://api.heygen.com\"\n    };\n    // State variables\n    const [sessionInfo, setSessionInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sessionToken, setSessionToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [room, setRoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mediaStream, setMediaStream] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [webSocket, setWebSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // const [avatarID, setAvatarID] = useState('Wayne_20240711');\n    const [avatarID, setAvatarID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Pedro_Chair_Sitting_public');\n    const [voiceID, setVoiceID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [taskInput, setTaskInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusMessages, setStatusMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isStarted, setIsStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAutoStarting, setIsAutoStarting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Voice input states\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recognition, setRecognition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isVoiceSupported, setIsVoiceSupported] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const mediaElementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const statusElementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Helper function to update status with enhanced styling\n    const updateStatus = function(message) {\n        let type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'info';\n        const timestamp = new Date().toLocaleTimeString();\n        const newMessage = {\n            message,\n            type,\n            timestamp\n        };\n        setStatusMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n    };\n    // Auto-scroll status to bottom\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QnAWithHeyGenModal.useEffect\": ()=>{\n            if (statusElementRef.current) {\n                statusElementRef.current.scrollTop = statusElementRef.current.scrollHeight;\n            }\n        }\n    }[\"QnAWithHeyGenModal.useEffect\"], [\n        statusMessages\n    ]);\n    // Initialize status messages and auto-start avatar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QnAWithHeyGenModal.useEffect\": ()=>{\n            if (isOpen) {\n                setStatusMessages([\n                    {\n                        message: \"Welcome to AI Avatar Assistant!\",\n                        type: \"system\",\n                        timestamp: new Date().toLocaleTimeString()\n                    },\n                    {\n                        message: \"Auto-starting avatar session...\",\n                        type: \"info\",\n                        timestamp: new Date().toLocaleTimeString()\n                    },\n                    {\n                        message: \"Please wait while we initialize your AI assistant\",\n                        type: \"info\",\n                        timestamp: new Date().toLocaleTimeString()\n                    }\n                ]);\n                // Set auto-starting state\n                setIsAutoStarting(true);\n                // Auto-start the avatar session after a brief delay to ensure component is ready\n                const autoStartTimer = setTimeout({\n                    \"QnAWithHeyGenModal.useEffect.autoStartTimer\": async ()=>{\n                        try {\n                            await handleStart();\n                            setIsAutoStarting(false);\n                        } catch (error) {\n                            console.error(\"Auto-start failed:\", error);\n                            setIsAutoStarting(false);\n                        }\n                    }\n                }[\"QnAWithHeyGenModal.useEffect.autoStartTimer\"], 1000); // 1 second delay\n                // Cleanup timer if component unmounts or modal closes\n                return ({\n                    \"QnAWithHeyGenModal.useEffect\": ()=>{\n                        clearTimeout(autoStartTimer);\n                        setIsAutoStarting(false);\n                    }\n                })[\"QnAWithHeyGenModal.useEffect\"];\n            } else {\n                // Reset states when modal closes\n                setIsAutoStarting(false);\n            }\n        }\n    }[\"QnAWithHeyGenModal.useEffect\"], [\n        isOpen\n    ]);\n    // Initialize voice recognition\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QnAWithHeyGenModal.useEffect\": ()=>{\n            if (true) {\n                // Check for Web Speech API support\n                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n                if (SpeechRecognition) {\n                    setIsVoiceSupported(true);\n                    const recognitionInstance = new SpeechRecognition();\n                    // Configure recognition\n                    recognitionInstance.continuous = false;\n                    recognitionInstance.interimResults = false;\n                    recognitionInstance.lang = 'en-US';\n                    // Handle results\n                    recognitionInstance.onresult = ({\n                        \"QnAWithHeyGenModal.useEffect\": (event)=>{\n                            const transcript = event.results[0][0].transcript;\n                            setTaskInput(transcript);\n                            updateStatus('\\uD83C\\uDFA4 Voice input: \"'.concat(transcript, '\"'), \"success\");\n                        }\n                    })[\"QnAWithHeyGenModal.useEffect\"];\n                    // Handle errors\n                    recognitionInstance.onerror = ({\n                        \"QnAWithHeyGenModal.useEffect\": (event)=>{\n                            updateStatus(\"Voice recognition error: \".concat(event.error), \"error\");\n                            setIsRecording(false);\n                        }\n                    })[\"QnAWithHeyGenModal.useEffect\"];\n                    // Handle end\n                    recognitionInstance.onend = ({\n                        \"QnAWithHeyGenModal.useEffect\": ()=>{\n                            setIsRecording(false);\n                        }\n                    })[\"QnAWithHeyGenModal.useEffect\"];\n                    setRecognition(recognitionInstance);\n                } else {\n                    setIsVoiceSupported(false);\n                    updateStatus(\"Voice input not supported in this browser\", \"warning\");\n                }\n            }\n        }\n    }[\"QnAWithHeyGenModal.useEffect\"], []);\n    // Get session token\n    const getSessionToken = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.create_token\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"X-Api-Key\": API_CONFIG.apiKey\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            if (!data.data || !data.data.token) {\n                throw new Error('Invalid token data received from server');\n            }\n            setSessionToken(data.data.token);\n            updateStatus(\"Session token obtained successfully\", \"success\");\n            return data.data.token;\n        } catch (error) {\n            updateStatus(\"Failed to get session token: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Connect WebSocket\n    const connectWebSocket = async (sessionId, token)=>{\n        try {\n            if (!token) {\n                throw new Error('No session token available for WebSocket connection');\n            }\n            const params = new URLSearchParams({\n                session_id: sessionId,\n                session_token: token,\n                silence_response: 'false',\n                opening_text: \"Hello, how can I help you?\",\n                stt_language: \"en\"\n            });\n            const wsUrl = \"wss://\".concat(new URL(API_CONFIG.serverUrl).hostname, \"/v1/ws/streaming.chat?\").concat(params);\n            const ws = new WebSocket(wsUrl);\n            setWebSocket(ws);\n            ws.addEventListener(\"message\", (event)=>{\n                const eventData = JSON.parse(event.data);\n                console.log(\"Raw WebSocket event:\", eventData);\n            });\n            ws.addEventListener(\"error\", (error)=>{\n                updateStatus(\"WebSocket connection error\", \"error\");\n                console.error(\"WebSocket error:\", error);\n            });\n            ws.addEventListener(\"open\", ()=>{\n                updateStatus(\"WebSocket connected successfully\", \"success\");\n            });\n        } catch (error) {\n            updateStatus(\"WebSocket connection failed: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Create new session\n    const createNewSession = async ()=>{\n        try {\n            // Always get a fresh token to avoid state timing issues\n            const token = await getSessionToken();\n            updateStatus(\"Debug: Creating session with token: \".concat(token ? 'Token exists' : 'No token'), \"info\");\n            const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.new\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    quality: \"high\",\n                    avatar_name: avatarID,\n                    voice: {\n                        voice_id: voiceID,\n                        rate: 1.0\n                    },\n                    version: \"v2\",\n                    video_encoding: \"H264\"\n                })\n            });\n            updateStatus(\"Debug: Create session response status: \".concat(response.status), \"info\");\n            if (!response.ok) {\n                const errorText = await response.text();\n                updateStatus(\"Debug: Create session error: \".concat(errorText), \"error\");\n                throw new Error(\"HTTP error! status: \".concat(response.status, \" - \").concat(errorText));\n            }\n            const data = await response.json();\n            if (!data.data || !data.data.session_id) {\n                throw new Error('Invalid session data received from server');\n            }\n            updateStatus(\"Debug: Session data keys: \".concat(Object.keys(data.data).join(', ')), \"info\");\n            // Set session info and return it for immediate use\n            setSessionInfo(data.data);\n            // Create LiveKit Room\n            const livekitRoom = new livekit_client__WEBPACK_IMPORTED_MODULE_2__.Room({\n                adaptiveStream: true,\n                dynacast: true,\n                videoCaptureDefaults: {\n                    resolution: livekit_client__WEBPACK_IMPORTED_MODULE_2__.VideoPresets.h720.resolution\n                }\n            });\n            setRoom(livekitRoom);\n            // Handle room events\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.DataReceived, (message)=>{\n                const data = new TextDecoder().decode(message);\n                console.log(\"Room message:\", JSON.parse(data));\n            });\n            // Handle media streams\n            const newMediaStream = new MediaStream();\n            setMediaStream(newMediaStream);\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.TrackSubscribed, (track)=>{\n                if (track.kind === \"video\" || track.kind === \"audio\") {\n                    newMediaStream.addTrack(track.mediaStreamTrack);\n                    if (newMediaStream.getVideoTracks().length > 0 && newMediaStream.getAudioTracks().length > 0) {\n                        if (mediaElementRef.current) {\n                            mediaElementRef.current.srcObject = newMediaStream;\n                            updateStatus(\"Media stream ready - Avatar is live!\", \"success\");\n                        }\n                    }\n                }\n            });\n            // Handle media stream removal\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.TrackUnsubscribed, (track)=>{\n                const mediaTrack = track.mediaStreamTrack;\n                if (mediaTrack) {\n                    newMediaStream.removeTrack(mediaTrack);\n                }\n            });\n            // Handle room connection state changes\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.Disconnected, (reason)=>{\n                updateStatus(\"Room disconnected: \".concat(reason), \"warning\");\n            });\n            await livekitRoom.prepareConnection(data.data.url, data.data.access_token);\n            updateStatus(\"Connection prepared successfully\", \"success\");\n            await connectWebSocket(data.data.session_id, token);\n            updateStatus(\"Session created successfully\", \"success\");\n            // Return both session data, token, and room for immediate use\n            return {\n                sessionData: data.data,\n                token,\n                room: livekitRoom\n            };\n        } catch (error) {\n            updateStatus(\"Failed to create session: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Start streaming session\n    const startStreamingSession = async (sessionData, token, livekitRoom)=>{\n        if (!sessionData || !sessionData.session_id) {\n            throw new Error('No session info available');\n        }\n        try {\n            updateStatus(\"Debug: Using token: \".concat(token ? 'Token exists' : 'No token'), \"info\");\n            updateStatus(\"Debug: Session ID: \".concat(sessionData.session_id), \"info\");\n            const startResponse = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.start\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    session_id: sessionData.session_id\n                })\n            });\n            updateStatus(\"Debug: Start response status: \".concat(startResponse.status), \"info\");\n            if (!startResponse.ok) {\n                const errorText = await startResponse.text();\n                updateStatus(\"Debug: Error response: \".concat(errorText), \"error\");\n                throw new Error(\"HTTP error! status: \".concat(startResponse.status, \" - \").concat(errorText));\n            }\n            // Connect to LiveKit room\n            updateStatus(\"Debug: Available session properties: \".concat(Object.keys(sessionData).join(', ')), \"info\");\n            updateStatus(\"Debug: Room exists: \".concat(!!livekitRoom), \"info\");\n            updateStatus(\"Debug: URL exists: \".concat(!!sessionData.url), \"info\");\n            updateStatus(\"Debug: Access token exists: \".concat(!!sessionData.access_token), \"info\");\n            if (livekitRoom && sessionData.url && sessionData.access_token) {\n                await livekitRoom.connect(sessionData.url, sessionData.access_token);\n                updateStatus(\"Connected to room successfully\", \"success\");\n            } else {\n                updateStatus(\"Warning: Room or connection details missing\", \"warning\");\n                updateStatus(\"Debug: Missing - Room: \".concat(!livekitRoom, \", URL: \").concat(!sessionData.url, \", Token: \").concat(!sessionData.access_token), \"error\");\n            }\n            setIsStarted(true);\n            updateStatus(\"Streaming started successfully\", \"success\");\n        } catch (error) {\n            updateStatus(\"Failed to start streaming: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Send text to avatar\n    const sendText = async function(text) {\n        let taskType = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"talk\";\n        if (!sessionInfo) {\n            updateStatus(\"No active session - please start the avatar first\", \"warning\");\n            return;\n        }\n        const token = sessionToken;\n        if (!token) {\n            updateStatus(\"No session token available\", \"error\");\n            return;\n        }\n        try {\n            updateStatus(\"Sending message to avatar...\", \"info\");\n            const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.task\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    session_id: sessionInfo.session_id,\n                    text: text,\n                    task_type: taskType\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || data.error) {\n                updateStatus(\"HeyGen API error: \".concat(data.error || response.statusText), \"error\");\n            } else {\n                updateStatus(\"✓ Message sent successfully (\".concat(taskType, \")\"), \"success\");\n                updateStatus('Avatar will speak: \"'.concat(text.substring(0, 50)).concat(text.length > 50 ? '...' : '', '\"'), \"info\");\n            }\n        } catch (err) {\n            updateStatus(\"HeyGen API call failed: \" + err.message, \"error\");\n        }\n    };\n    // Close session\n    const closeSession = async ()=>{\n        if (!sessionInfo) {\n            updateStatus(\"No active session\", \"warning\");\n            return;\n        }\n        try {\n            const token = sessionToken;\n            if (token) {\n                const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.stop\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        Authorization: \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        session_id: sessionInfo.session_id\n                    })\n                });\n            }\n            // Close WebSocket\n            if (webSocket) {\n                webSocket.close();\n            }\n            // Disconnect from LiveKit room\n            if (room) {\n                room.disconnect();\n            }\n            // Reset states\n            if (mediaElementRef.current) {\n                mediaElementRef.current.srcObject = null;\n            }\n            setSessionInfo(null);\n            setRoom(null);\n            setMediaStream(null);\n            setSessionToken(null);\n            setIsStarted(false);\n            updateStatus(\"Session closed successfully\", \"warning\");\n        } catch (error) {\n            updateStatus(\"Error closing session: \".concat(error.message), \"error\");\n            // Still reset states even if API call fails\n            setSessionInfo(null);\n            setRoom(null);\n            setMediaStream(null);\n            setSessionToken(null);\n            setIsStarted(false);\n        }\n    };\n    // Azure Search and OpenAI integration\n    const callAzureOpenAI = async (prompt)=>{\n        try {\n            var _data_choices__message, _data_choices_, _data_choices;\n            const response = await fetch(\"http://localhost:5001/api/openai\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    prompt\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || !data.choices) {\n                const errorMsg = data.error ? typeof data.error === 'object' ? JSON.stringify(data.error) : data.error : response.statusText;\n                updateStatus(\"Azure OpenAI error: \" + errorMsg, \"error\");\n                return null;\n            }\n            return (_data_choices = data.choices) === null || _data_choices === void 0 ? void 0 : (_data_choices_ = _data_choices[0]) === null || _data_choices_ === void 0 ? void 0 : (_data_choices__message = _data_choices_.message) === null || _data_choices__message === void 0 ? void 0 : _data_choices__message.content;\n        } catch (err) {\n            updateStatus(\"Azure OpenAI call failed: \" + err.message, \"error\");\n            return null;\n        }\n    };\n    const searchAzure = async (query)=>{\n        try {\n            const response = await fetch(\"http://localhost:5001/api/search\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    query,\n                    courseId\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || !data.value) {\n                const errorMsg = data.error ? typeof data.error === 'object' ? JSON.stringify(data.error) : data.error : response.statusText;\n                updateStatus(\"Azure Search error: \" + errorMsg, \"error\");\n                return null;\n            }\n            updateStatus(\"Found \".concat(data.value.length, \" relevant documents\"), \"success\");\n            return (data.value || []).map((r)=>r.content).join('\\n\\n');\n        } catch (err) {\n            updateStatus(\"Azure Search call failed: \" + err.message, \"error\");\n            return null;\n        }\n    };\n    // Handle Ask AI button\n    const handleAskAI = async ()=>{\n        const text = taskInput.trim();\n        if (!text) {\n            updateStatus(\"Please enter a question first\", \"warning\");\n            return;\n        }\n        if (!sessionInfo) {\n            updateStatus(\"Please start the avatar session first\", \"warning\");\n            return;\n        }\n        try {\n            updateStatus('\\uD83D\\uDD0D Searching for: \"'.concat(text, '\"'), \"info\");\n            const searchResults = await searchAzure(text);\n            if (!searchResults) {\n                updateStatus(\"No relevant documents found for your question\", \"warning\");\n                return;\n            }\n            updateStatus(\"🤖 Processing with AI...\", \"info\");\n            const llmAnswer = await callAzureOpenAI('Based on the following context, answer the question: \"'.concat(text, '\"\\n\\nContext: ').concat(searchResults));\n            if (llmAnswer) {\n                updateStatus(\"\\uD83D\\uDCA1 AI Response: \".concat(llmAnswer.substring(0, 100)).concat(llmAnswer.length > 100 ? '...' : ''), \"success\");\n                // Clean the response for avatar speech\n                let cleaned = llmAnswer.replace(/[#*_`>-]+/g, '').replace(/\\n{2,}/g, ' ').replace(/\\s{2,}/g, ' ').trim();\n                updateStatus(\"\\uD83C\\uDFA4 Avatar speaking response...\", \"info\");\n                await sendText(cleaned, \"repeat\");\n                setTaskInput(\"\");\n            } else {\n                updateStatus(\"Failed to generate AI response\", \"error\");\n            }\n        } catch (error) {\n            updateStatus(\"Error processing question: \".concat(error.message), \"error\");\n        }\n    };\n    // Handle Voice Input button\n    const handleVoiceInput = ()=>{\n        if (!isVoiceSupported) {\n            updateStatus(\"Voice input not supported in this browser\", \"warning\");\n            return;\n        }\n        if (!recognition) {\n            updateStatus(\"Voice recognition not initialized\", \"error\");\n            return;\n        }\n        if (isRecording) {\n            // Stop recording\n            recognition.stop();\n            setIsRecording(false);\n            updateStatus(\"Voice recording stopped\", \"info\");\n        } else {\n            // Start recording\n            try {\n                recognition.start();\n                setIsRecording(true);\n                updateStatus(\"🎤 Listening... Speak your question\", \"info\");\n            } catch (error) {\n                updateStatus(\"Failed to start voice recording: \".concat(error.message), \"error\");\n                setIsRecording(false);\n            }\n        }\n    };\n    // Handle Start button\n    const handleStart = async ()=>{\n        try {\n            const result = await createNewSession();\n            await startStreamingSession(result.sessionData, result.token, result.room);\n        } catch (error) {\n            updateStatus(\"Error starting session: \".concat(error.message), \"error\");\n        }\n    };\n    // Handle Enter key in textarea\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleAskAI();\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-white\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold\",\n                                                children: \"AI Avatar Assistant\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white text-opacity-70 text-sm\",\n                                                children: \"Powered by EXL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 610,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 604,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full \".concat(isStarted ? 'bg-green-400 animate-pulse' : 'bg-gray-400')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-opacity-80 text-sm font-medium\",\n                                                children: isStarted ? 'Live' : 'Offline'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 616,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        className: \"text-white hover:text-gray-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 615,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                        lineNumber: 603,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                    lineNumber: 602,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 max-h-[calc(90vh-120px)] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-2xl p-6 border border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800 mb-6 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2 text-blue-600\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Avatar Configuration\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Avatar ID\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 651,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: \"Enter Avatar ID\",\n                                                                value: avatarID,\n                                                                onChange: (e)=>setAvatarID(e.target.value),\n                                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 652,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-3 mt-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleStart,\n                                                                disabled: isStarted || isAutoStarting,\n                                                                className: \"px-4 py-3 bg-green-500 hover:bg-green-600 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none shadow-lg\",\n                                                                children: isAutoStarting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 inline mr-2 animate-spin\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                                lineNumber: 682,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 681,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \"Auto-Starting...\"\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 inline mr-2\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                                lineNumber: 689,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 688,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        isStarted ? 'Connected' : 'Start'\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 674,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: closeSession,\n                                                                className: \"px-4 py-3 bg-red-500 hover:bg-red-600 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 inline mr-2\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 700,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 699,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \"Stop\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 695,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-2xl p-6 border border-gray-200 mt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-800 mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2 text-cyan-600\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 712,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Ask Your Question\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            placeholder: \"Type your question here...\",\n                                                            value: taskInput,\n                                                            onChange: (e)=>setTaskInput(e.target.value),\n                                                            onKeyDown: handleKeyDown,\n                                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-200 resize-none h-24\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 719,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleAskAI,\n                                                                className: \"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 inline mr-2\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 734,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 733,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \"Ask AI\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleVoiceInput,\n                                                                disabled: !isVoiceSupported,\n                                                                className: \"px-4 py-3 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg \".concat(isRecording ? 'bg-red-500 hover:bg-red-600 animate-pulse' : isVoiceSupported ? 'bg-purple-500 hover:bg-purple-600' : 'bg-gray-400 cursor-not-allowed'),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 inline mr-2\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a2 2 0 114 0v4a2 2 0 11-4 0V7z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 751,\n                                                                            columnNumber: 27\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 753,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 749,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    isRecording ? 'Stop Recording' : 'Voice Input'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 738,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 709,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 639,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-2xl p-6 border border-gray-200 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-semibold text-gray-800 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 mr-2 text-blue-600\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M2 6a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 770,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 769,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"AI Avatar\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 768,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 775,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Ready\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 776,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 774,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 767,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                ref: mediaElementRef,\n                                                className: \"w-full h-96 bg-gray-900 rounded-xl object-cover shadow-lg\",\n                                                autoPlay: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-16 h-16 mx-auto mb-4 opacity-50\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                    lineNumber: 789,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 788,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-medium\",\n                                                                children: \"Avatar will appear here\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 791,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm opacity-75\",\n                                                                children: \"Click Start to begin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 792,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 787,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 786,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 781,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 780,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                    lineNumber: 766,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 764,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                        lineNumber: 636,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                    lineNumber: 635,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n            lineNumber: 600,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n        lineNumber: 599,\n        columnNumber: 5\n    }, undefined);\n};\n_s(QnAWithHeyGenModal, \"KD4zp0K+Vr4yX6MzZrH14V/PMpU=\");\n_c = QnAWithHeyGenModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QnAWithHeyGenModal);\nvar _c;\n$RefreshReg$(_c, \"QnAWithHeyGenModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/QnAWithHeyGenModal.tsx\n"));

/***/ })

});