"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/avatar-video/page",{

/***/ "(app-pages-browser)/./src/app/admin/AvatarVideoPanel.tsx":
/*!********************************************!*\
  !*** ./src/app/admin/AvatarVideoPanel.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AvatarVideoPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clapperboard.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-user-round.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction AvatarVideoPanel() {\n    var _avatars_find, _uniqueCourses_find, _filteredVersions_find;\n    _s();\n    const [avatars, setAvatars] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]);\n    const [selectedAvatarId, setSelectedAvatarId] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null);\n    const [approvedVersions, setApprovedVersions] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]);\n    const [selectedCourseIdForVideo, setSelectedCourseIdForVideo] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null);\n    const [selectedVersionId, setSelectedVersionId] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null);\n    const [loadingVersions, setLoadingVersions] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(true);\n    const [loadingAvatars, setLoadingAvatars] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(true);\n    const [generatingVideos, setGeneratingVideos] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false);\n    const [videoGenerationResults, setVideoGenerationResults] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]);\n    const [showConfirmDialog, setShowConfirmDialog] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false);\n    const [generationProgress, setGenerationProgress] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)({\n        current: 0,\n        total: 0\n    });\n    const [currentGeneratingPage, setCurrentGeneratingPage] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)('');\n    // Auto-generation states\n    const [autoStartCountdown, setAutoStartCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null);\n    const [hasAutoStarted, setHasAutoStarted] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false);\n    const countdownIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_8__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_8__.useEffect)({\n        \"AvatarVideoPanel.useEffect\": ()=>{\n            const fetchApprovedVersions = {\n                \"AvatarVideoPanel.useEffect.fetchApprovedVersions\": async ()=>{\n                    try {\n                        const res = await fetch(\"http://localhost:5001/api/all-approved-content-versions\");\n                        const data = await res.json();\n                        setApprovedVersions(data);\n                    } catch (err) {\n                        console.error(\"Failed to fetch approved versions:\", err);\n                        alert(\"Failed to load approved content versions.\");\n                    } finally{\n                        setLoadingVersions(false);\n                    }\n                }\n            }[\"AvatarVideoPanel.useEffect.fetchApprovedVersions\"];\n            const fetchAvatars = {\n                \"AvatarVideoPanel.useEffect.fetchAvatars\": async ()=>{\n                    try {\n                        const res = await fetch(\"http://localhost:5001/api/avatars\");\n                        const data = await res.json();\n                        if (Array.isArray(data)) {\n                            setAvatars(data);\n                        } else {\n                            console.error(\"API returned non-array data for avatars:\", data);\n                            setAvatars([]);\n                            alert(\"Failed to load avatar configurations: Invalid data format.\");\n                        }\n                    } catch (err) {\n                        console.error(\"Failed to fetch avatars:\", err);\n                        alert(\"Failed to load avatar configurations. Please ensure backend is running.\");\n                    } finally{\n                        setLoadingAvatars(false);\n                    }\n                }\n            }[\"AvatarVideoPanel.useEffect.fetchAvatars\"];\n            fetchApprovedVersions();\n            fetchAvatars();\n        }\n    }[\"AvatarVideoPanel.useEffect\"], []);\n    const uniqueCourses = approvedVersions.reduce((acc, version)=>{\n        if (!acc.find((course)=>course.course_id === version.course_id)) {\n            acc.push({\n                course_id: version.course_id,\n                course_title: version.course_title\n            });\n        }\n        return acc;\n    }, []);\n    const filteredVersions = approvedVersions.filter((version)=>version.course_id === Number(selectedCourseIdForVideo));\n    const handleGenerateVideosClick = ()=>{\n        if (!selectedAvatarId || !selectedVersionId) {\n            alert(\"Please select both an avatar and a content version.\");\n            return;\n        }\n        setShowConfirmDialog(true);\n    };\n    const handleConfirmGeneration = async ()=>{\n        setShowConfirmDialog(false);\n        setGeneratingVideos(true);\n        setVideoGenerationResults([]);\n        setGenerationProgress({\n            current: 0,\n            total: 0\n        });\n        setCurrentGeneratingPage('');\n        try {\n            // First get the content to know total pages\n            const versionRes = await fetch(\"http://localhost:5001/api/course-content-versions/\".concat(selectedVersionId));\n            const versionData = await versionRes.json();\n            const totalPages = Array.isArray(versionData.content) ? versionData.content.length : 0;\n            setGenerationProgress({\n                current: 0,\n                total: totalPages\n            });\n            const res = await fetch(\"http://localhost:5001/api/generate-course-videos\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    selectedAvatarId,\n                    selectedVersionId\n                })\n            });\n            const result = await res.json();\n            if (!res.ok) {\n                throw new Error(result.error || \"Failed to initiate video generation\");\n            }\n            console.log(\"Generated Videos Data:\", result.generated_videos);\n            setVideoGenerationResults(result.generated_videos || []);\n        } catch (error) {\n            console.error(\"Video generation error:\", error);\n            alert(\"Video generation failed: \".concat(error.message || \"Unknown error\"));\n        } finally{\n            setGeneratingVideos(false);\n            setCurrentGeneratingPage('');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"mb-8 border-0 shadow-xl bg-white/80 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20 h-20 bg-orange-400 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-10 h-10 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold mb-3 text-gray-900 bg-brand-blue bg-clip-text text-transparent\",\n                                    children: \"Medical Course Avatar & Video Generation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n                                    children: \"Select a medical professional avatar and generate engaging videos from your approved summaries with cutting-edge AI technology\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {\n                            className: \"my-8\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"p-4 transition-all duration-300 \".concat(selectedAvatarId ? 'border-blue-500 bg-blue-50 shadow-lg scale-105' : 'border-gray-200 hover:shadow-md'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-xl flex items-center justify-center transition-colors \".concat(selectedAvatarId ? 'bg-blue-500' : 'bg-gray-200'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-6 h-6 \".concat(selectedAvatarId ? 'text-white' : 'text-gray-400')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold \".concat(selectedAvatarId ? 'text-blue-600' : 'text-gray-500'),\n                                                        children: \"Select Avatar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Choose your presenter\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"p-4 transition-all duration-300 \".concat(selectedVersionId ? 'border-green-500 bg-green-50 shadow-lg scale-105' : 'border-gray-200 hover:shadow-md'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-xl flex items-center justify-center transition-colors \".concat(selectedVersionId ? 'bg-green-500' : 'bg-gray-200'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-6 h-6 \".concat(selectedVersionId ? 'text-white' : 'text-gray-400')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold \".concat(selectedVersionId ? 'text-green-600' : 'text-gray-500'),\n                                                        children: \"Select Content\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Choose course material\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"p-4 transition-all duration-300 \".concat(generatingVideos ? 'border-amber-500 bg-amber-50 shadow-lg scale-105' : 'border-gray-200 hover:shadow-md'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-xl flex items-center justify-center transition-colors \".concat(generatingVideos ? 'bg-amber-500' : 'bg-gray-200'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-6 h-6 \".concat(generatingVideos ? 'text-white' : 'text-gray-400')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold \".concat(generatingVideos ? 'text-amber-600' : 'text-gray-500'),\n                                                        children: \"Generate Videos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Create content\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"mb-8 border-0 shadow-xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        className: \"pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-7 h-7 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"text-2xl text-gray-900\",\n                                            children: \"Select Medical Professional Avatar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mt-1\",\n                                            children: \"Choose your video presenter from our collection of medical professionals\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loadingAvatars ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"Loading avatars...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, this) : avatars.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"p-12 text-center bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDC65\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 text-lg\",\n                                    children: \"No avatars available.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                            children: avatars.map((a)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"group relative cursor-pointer transition-all duration-300 hover:shadow-xl \".concat(selectedAvatarId === a.id ? \"border-2 border-blue-500 bg-blue-50 shadow-xl scale-105\" : \"border hover:border-blue-300 hover:bg-blue-50/50 hover:scale-102\"),\n                                    onClick: ()=>setSelectedAvatarId(a.id),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"p-6 text-center\",\n                                        style: {\n                                            minHeight: 280\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center transition-all \".concat(selectedAvatarId === a.id ? 'bg-blue-500 shadow-lg' : 'bg-orange-100 group-hover:bg-blue-100'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-8 h-8 transition-colors \".concat(selectedAvatarId === a.id ? 'text-white' : 'text-orange-600 group-hover:text-blue-600')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-gray-900 mb-3 text-lg truncate\",\n                                                title: a.avatar_name,\n                                                children: a.avatar_name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"text-xs\",\n                                                        children: a.specialty || \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500 truncate\",\n                                                        title: a.domain || \"N/A\",\n                                                        children: a.domain || \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-orange-600 truncate\",\n                                                        title: a.voice_id || \"N/A\",\n                                                        children: [\n                                                            \"Voice: \",\n                                                            a.voice_id || \"N/A\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: selectedAvatarId === a.id ? \"default\" : \"outline\",\n                                                className: \"w-full transition-all duration-200 \".concat(selectedAvatarId === a.id ? \"bg-blue-600 hover:bg-blue-700 text-white shadow-md\" : \"hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300\"),\n                                                children: selectedAvatarId === a.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        \"Selected\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 25\n                                                }, this) : \"Select Avatar\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 19\n                                    }, this)\n                                }, a.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            selectedAvatarId !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"mb-8 border-0 shadow-xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        className: \"pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-7 h-7 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"text-2xl text-gray-900\",\n                                            children: \"Select Course Content for Video Generation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mt-1\",\n                                            children: \"Choose your course and approved version for video creation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loadingVersions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-2 border-gray-300 border-t-green-500 rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"Loading approved content versions...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 15\n                        }, this) : approvedVersions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"p-12 text-center bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDCDA\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 text-lg\",\n                                    children: \"No approved content versions available.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_2__.Label, {\n                                            htmlFor: \"course\",\n                                            className: \"text-base font-semibold text-gray-700\",\n                                            children: \"Course Selection\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                            value: selectedCourseIdForVideo || '',\n                                            onValueChange: (value)=>{\n                                                setSelectedCourseIdForVideo(value);\n                                                setSelectedVersionId(null);\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                                    className: \"w-full h-14 text-base focus:ring-2 focus:ring-green-200 border-2 rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                                        placeholder: \"Choose a course to generate videos from\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                                    children: uniqueCourses.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                            value: course.course_id.toString(),\n                                                            className: \"text-base py-3\",\n                                                            children: course.course_title\n                                                        }, course.course_id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 17\n                                }, this),\n                                selectedCourseIdForVideo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_2__.Label, {\n                                            htmlFor: \"version\",\n                                            className: \"text-base font-semibold text-gray-700\",\n                                            children: \"Version Selection\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                            value: selectedVersionId || '',\n                                            onValueChange: (value)=>{\n                                                setSelectedVersionId(value);\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                                    className: \"w-full h-14 text-base focus:ring-2 focus:ring-green-200 border-2 rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                                        placeholder: \"Select an approved version\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                                    children: filteredVersions.map((version)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                            value: version.version_id.toString(),\n                                                            className: \"text-base py-3\",\n                                                            children: [\n                                                                \"Version \",\n                                                                version.version_number,\n                                                                \" (Approved: \",\n                                                                new Date(version.approved_at).toLocaleDateString(),\n                                                                \")\"\n                                                            ]\n                                                        }, version.version_id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 284,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"border-0 shadow-xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        className: \"pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-amber-100 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-7 h-7 text-amber-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"text-2xl text-gray-900\",\n                                                    children: \"Video Generation Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mt-1\",\n                                                    children: \"Generate professional video content from your selections\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: 'warning',\n                                    size: \"lg\",\n                                    className: \"flex items-center space-x-3 px-8 py-4 text-white font-semibold rounded-2xl shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105\",\n                                    onClick: handleGenerateVideosClick,\n                                    disabled: !selectedAvatarId || !selectedVersionId || generatingVideos,\n                                    children: generatingVideos ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"animate-spin w-6 h-6 text-white\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                        className: \"opacity-25\",\n                                                        cx: \"12\",\n                                                        cy: \"12\",\n                                                        r: \"10\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        className: \"opacity-75\",\n                                                        fill: \"currentColor\",\n                                                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: \"Generating Videos...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: \"Generate All Videos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: [\n                            generatingVideos && generationProgress.total > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"mb-8 bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-blue-900 text-lg flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-5 h-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Generation Progress\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"bg-blue-100 text-blue-800 font-semibold\",\n                                                    children: [\n                                                        generationProgress.current,\n                                                        \" of \",\n                                                        generationProgress.total,\n                                                        \" completed\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_6__.Progress, {\n                                            value: generationProgress.current / generationProgress.total * 100,\n                                            className: \"h-3 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 17\n                                        }, this),\n                                        currentGeneratingPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-700 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Currently generating: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium ml-1\",\n                                                    children: currentGeneratingPage\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 43\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 13\n                            }, this),\n                            videoGenerationResults.length === 0 && !generatingVideos && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-32 h-32 bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-16 h-16 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                        children: \"Ready to Generate Videos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 max-w-lg mx-auto mb-8 text-lg leading-relaxed\",\n                                        children: 'Select an avatar and course content, then click \"Generate All Videos\" to begin creating your professional video content with AI.'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 15\n                                    }, this),\n                                    (!selectedAvatarId || !selectedVersionId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-200 max-w-md mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2 text-amber-700 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-lg\",\n                                                            children: \"Selection Required\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-amber-600\",\n                                                    children: \"Please select both an avatar and course content to proceed with video generation.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 17\n                                    }, this),\n                                    selectedAvatarId && selectedVersionId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200 max-w-md mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2 text-green-700 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-lg\",\n                                                            children: \"Ready to Generate!\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-green-600\",\n                                                    children: \"Avatar and content selected. Click the button above to start video generation.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 13\n                            }, this),\n                            videoGenerationResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-6 h-6 text-gray-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-bold text-gray-800 text-xl\",\n                                                children: \"Generated Videos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"bg-green-100 text-green-800\",\n                                                children: [\n                                                    videoGenerationResults.length,\n                                                    \" videos processed\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-4\",\n                                        children: videoGenerationResults.map((page, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                className: \"hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center mb-4\",\n                                                                    children: [\n                                                                        page.generation_status === \"success\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 bg-green-500 rounded-full mr-4 flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-white\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                                lineNumber: 487,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 486,\n                                                                            columnNumber: 31\n                                                                        }, this) : page.generation_status === \"skipped_empty_script\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 bg-yellow-500 rounded-full mr-4 flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-white\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                                lineNumber: 491,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 490,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 bg-red-500 rounded-full mr-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 494,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                            className: \"font-bold text-gray-800 text-xl\",\n                                                                            children: page.title || \"Chapter \".concat(page.page)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 496,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 484,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"mb-4\",\n                                                                    children: [\n                                                                        \"Page \",\n                                                                        page.page\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 500,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                page.generation_status === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: \"bg-green-100 text-green-800 hover:bg-green-100\",\n                                                                    children: \"✅ Generated Successfully\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                page.generation_status === \"skipped_empty_script\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: \"bg-yellow-100 text-yellow-800 hover:bg-yellow-100\",\n                                                                    children: \"⚠️ Skipped (Empty Script)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 511,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                page.generation_status === \"failed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            className: \"bg-red-100 text-red-800 hover:bg-red-100\",\n                                                                            children: \"❌ Generation Failed\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 518,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-red-600 mt-2 p-3 bg-red-50 rounded-lg border border-red-200\",\n                                                                            children: page.error_details || \"Unknown error occurred during generation\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 521,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 517,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 362,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: showConfirmDialog,\n                onOpenChange: setShowConfirmDialog,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    className: \"max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20 h-20 bg-gradient-to-br from-amber-400 to-orange-500 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-10 h-10 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                    className: \"text-2xl font-bold text-gray-800 mb-4\",\n                                    children: \"Generate All Videos?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-6 text-lg leading-relaxed\",\n                                        children: [\n                                            \"This will generate videos for all pages in the selected course content.\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 88\n                                            }, this),\n                                            \"This process may take several minutes to complete.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 540,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-200 mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center font-bold mb-4 text-amber-900 text-lg\",\n                                        children: \"Selected Configuration\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-6 h-6 text-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-amber-800\",\n                                                            children: \"Avatar\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-amber-700 break-words\",\n                                                            children: (_avatars_find = avatars.find((a)=>a.id === selectedAvatarId)) === null || _avatars_find === void 0 ? void 0 : _avatars_find.avatar_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-6 h-6 text-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 568,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-amber-800\",\n                                                            children: \"Course\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-amber-700 break-words\",\n                                                            children: (_uniqueCourses_find = uniqueCourses.find((c)=>c.course_id === Number(selectedCourseIdForVideo))) === null || _uniqueCourses_find === void 0 ? void 0 : _uniqueCourses_find.course_title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-6 h-6 text-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-amber-800\",\n                                                            children: \"Version\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-amber-700 break-words\",\n                                                            children: (_filteredVersions_find = filteredVersions.find((v)=>v.version_id === selectedVersionId)) === null || _filteredVersions_find === void 0 ? void 0 : _filteredVersions_find.version_number\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 554,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogFooter, {\n                            className: \"flex space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"outline\",\n                                    className: \"flex-1 h-12 text-base\",\n                                    onClick: ()=>setShowConfirmDialog(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"brand\",\n                                    className: \"flex-1 h-12 text-base\",\n                                    onClick: handleConfirmGeneration,\n                                    children: \"Yes, Generate Videos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 588,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                    lineNumber: 539,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 538,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n_s(AvatarVideoPanel, \"tEgiH7+z2MFRwnT/9p2fp4GFLX4=\");\n_c = AvatarVideoPanel;\nvar _c;\n$RefreshReg$(_c, \"AvatarVideoPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/AvatarVideoPanel.tsx\n"));

/***/ })

});