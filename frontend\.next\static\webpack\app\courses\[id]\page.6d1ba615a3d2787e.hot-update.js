"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/courses/[id]/page",{

/***/ "(app-pages-browser)/./src/components/QnAWithHeyGenModal.tsx":
/*!***********************************************!*\
  !*** ./src/components/QnAWithHeyGenModal.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var livekit_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! livekit-client */ \"(app-pages-browser)/./node_modules/livekit-client/dist/livekit-client.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst QnAWithHeyGenModal = (param)=>{\n    let { isOpen, onClose, courseId } = param;\n    _s();\n    // Configuration\n    const API_CONFIG = {\n        apiKey: \"NDM2Y2M0NDI0YTMzNGM1OWFkZThhZTRmMjJhZmEzNzYtMTc0NzA1OTcyOA==\",\n        serverUrl: \"https://api.heygen.com\"\n    };\n    // State variables\n    const [sessionInfo, setSessionInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sessionToken, setSessionToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [room, setRoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mediaStream, setMediaStream] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [webSocket, setWebSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // const [avatarID, setAvatarID] = useState('Wayne_20240711');\n    const [avatarID, setAvatarID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Pedro_Chair_Sitting_public');\n    const [voiceID, setVoiceID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [taskInput, setTaskInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusMessages, setStatusMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isStarted, setIsStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Voice input states\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recognition, setRecognition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isVoiceSupported, setIsVoiceSupported] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const mediaElementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const statusElementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Helper function to update status with enhanced styling\n    const updateStatus = function(message) {\n        let type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'info';\n        const timestamp = new Date().toLocaleTimeString();\n        const newMessage = {\n            message,\n            type,\n            timestamp\n        };\n        setStatusMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n    };\n    // Auto-scroll status to bottom\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QnAWithHeyGenModal.useEffect\": ()=>{\n            if (statusElementRef.current) {\n                statusElementRef.current.scrollTop = statusElementRef.current.scrollHeight;\n            }\n        }\n    }[\"QnAWithHeyGenModal.useEffect\"], [\n        statusMessages\n    ]);\n    // Initialize status messages and auto-start avatar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QnAWithHeyGenModal.useEffect\": ()=>{\n            if (isOpen) {\n                setStatusMessages([\n                    {\n                        message: \"Welcome to AI Avatar Assistant!\",\n                        type: \"system\",\n                        timestamp: new Date().toLocaleTimeString()\n                    },\n                    {\n                        message: \"Auto-starting avatar session...\",\n                        type: \"info\",\n                        timestamp: new Date().toLocaleTimeString()\n                    },\n                    {\n                        message: \"Please wait while we initialize your AI assistant\",\n                        type: \"info\",\n                        timestamp: new Date().toLocaleTimeString()\n                    }\n                ]);\n                // Auto-start the avatar session after a brief delay to ensure component is ready\n                const autoStartTimer = setTimeout({\n                    \"QnAWithHeyGenModal.useEffect.autoStartTimer\": async ()=>{\n                        try {\n                            await handleStart();\n                        } catch (error) {\n                            console.error(\"Auto-start failed:\", error);\n                        }\n                    }\n                }[\"QnAWithHeyGenModal.useEffect.autoStartTimer\"], 1000); // 1 second delay\n                // Cleanup timer if component unmounts or modal closes\n                return ({\n                    \"QnAWithHeyGenModal.useEffect\": ()=>{\n                        clearTimeout(autoStartTimer);\n                    }\n                })[\"QnAWithHeyGenModal.useEffect\"];\n            }\n        }\n    }[\"QnAWithHeyGenModal.useEffect\"], [\n        isOpen\n    ]);\n    // Initialize voice recognition\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QnAWithHeyGenModal.useEffect\": ()=>{\n            if (true) {\n                // Check for Web Speech API support\n                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n                if (SpeechRecognition) {\n                    setIsVoiceSupported(true);\n                    const recognitionInstance = new SpeechRecognition();\n                    // Configure recognition\n                    recognitionInstance.continuous = false;\n                    recognitionInstance.interimResults = false;\n                    recognitionInstance.lang = 'en-US';\n                    // Handle results\n                    recognitionInstance.onresult = ({\n                        \"QnAWithHeyGenModal.useEffect\": (event)=>{\n                            const transcript = event.results[0][0].transcript;\n                            setTaskInput(transcript);\n                            updateStatus('\\uD83C\\uDFA4 Voice input: \"'.concat(transcript, '\"'), \"success\");\n                        }\n                    })[\"QnAWithHeyGenModal.useEffect\"];\n                    // Handle errors\n                    recognitionInstance.onerror = ({\n                        \"QnAWithHeyGenModal.useEffect\": (event)=>{\n                            updateStatus(\"Voice recognition error: \".concat(event.error), \"error\");\n                            setIsRecording(false);\n                        }\n                    })[\"QnAWithHeyGenModal.useEffect\"];\n                    // Handle end\n                    recognitionInstance.onend = ({\n                        \"QnAWithHeyGenModal.useEffect\": ()=>{\n                            setIsRecording(false);\n                        }\n                    })[\"QnAWithHeyGenModal.useEffect\"];\n                    setRecognition(recognitionInstance);\n                } else {\n                    setIsVoiceSupported(false);\n                    updateStatus(\"Voice input not supported in this browser\", \"warning\");\n                }\n            }\n        }\n    }[\"QnAWithHeyGenModal.useEffect\"], []);\n    // Get session token\n    const getSessionToken = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.create_token\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"X-Api-Key\": API_CONFIG.apiKey\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            if (!data.data || !data.data.token) {\n                throw new Error('Invalid token data received from server');\n            }\n            setSessionToken(data.data.token);\n            updateStatus(\"Session token obtained successfully\", \"success\");\n            return data.data.token;\n        } catch (error) {\n            updateStatus(\"Failed to get session token: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Connect WebSocket\n    const connectWebSocket = async (sessionId, token)=>{\n        try {\n            if (!token) {\n                throw new Error('No session token available for WebSocket connection');\n            }\n            const params = new URLSearchParams({\n                session_id: sessionId,\n                session_token: token,\n                silence_response: 'false',\n                opening_text: \"Hello, how can I help you?\",\n                stt_language: \"en\"\n            });\n            const wsUrl = \"wss://\".concat(new URL(API_CONFIG.serverUrl).hostname, \"/v1/ws/streaming.chat?\").concat(params);\n            const ws = new WebSocket(wsUrl);\n            setWebSocket(ws);\n            ws.addEventListener(\"message\", (event)=>{\n                const eventData = JSON.parse(event.data);\n                console.log(\"Raw WebSocket event:\", eventData);\n            });\n            ws.addEventListener(\"error\", (error)=>{\n                updateStatus(\"WebSocket connection error\", \"error\");\n                console.error(\"WebSocket error:\", error);\n            });\n            ws.addEventListener(\"open\", ()=>{\n                updateStatus(\"WebSocket connected successfully\", \"success\");\n            });\n        } catch (error) {\n            updateStatus(\"WebSocket connection failed: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Create new session\n    const createNewSession = async ()=>{\n        try {\n            // Always get a fresh token to avoid state timing issues\n            const token = await getSessionToken();\n            updateStatus(\"Debug: Creating session with token: \".concat(token ? 'Token exists' : 'No token'), \"info\");\n            const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.new\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    quality: \"high\",\n                    avatar_name: avatarID,\n                    voice: {\n                        voice_id: voiceID,\n                        rate: 1.0\n                    },\n                    version: \"v2\",\n                    video_encoding: \"H264\"\n                })\n            });\n            updateStatus(\"Debug: Create session response status: \".concat(response.status), \"info\");\n            if (!response.ok) {\n                const errorText = await response.text();\n                updateStatus(\"Debug: Create session error: \".concat(errorText), \"error\");\n                throw new Error(\"HTTP error! status: \".concat(response.status, \" - \").concat(errorText));\n            }\n            const data = await response.json();\n            if (!data.data || !data.data.session_id) {\n                throw new Error('Invalid session data received from server');\n            }\n            updateStatus(\"Debug: Session data keys: \".concat(Object.keys(data.data).join(', ')), \"info\");\n            // Set session info and return it for immediate use\n            setSessionInfo(data.data);\n            // Create LiveKit Room\n            const livekitRoom = new livekit_client__WEBPACK_IMPORTED_MODULE_2__.Room({\n                adaptiveStream: true,\n                dynacast: true,\n                videoCaptureDefaults: {\n                    resolution: livekit_client__WEBPACK_IMPORTED_MODULE_2__.VideoPresets.h720.resolution\n                }\n            });\n            setRoom(livekitRoom);\n            // Handle room events\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.DataReceived, (message)=>{\n                const data = new TextDecoder().decode(message);\n                console.log(\"Room message:\", JSON.parse(data));\n            });\n            // Handle media streams\n            const newMediaStream = new MediaStream();\n            setMediaStream(newMediaStream);\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.TrackSubscribed, (track)=>{\n                if (track.kind === \"video\" || track.kind === \"audio\") {\n                    newMediaStream.addTrack(track.mediaStreamTrack);\n                    if (newMediaStream.getVideoTracks().length > 0 && newMediaStream.getAudioTracks().length > 0) {\n                        if (mediaElementRef.current) {\n                            mediaElementRef.current.srcObject = newMediaStream;\n                            updateStatus(\"Media stream ready - Avatar is live!\", \"success\");\n                        }\n                    }\n                }\n            });\n            // Handle media stream removal\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.TrackUnsubscribed, (track)=>{\n                const mediaTrack = track.mediaStreamTrack;\n                if (mediaTrack) {\n                    newMediaStream.removeTrack(mediaTrack);\n                }\n            });\n            // Handle room connection state changes\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.Disconnected, (reason)=>{\n                updateStatus(\"Room disconnected: \".concat(reason), \"warning\");\n            });\n            await livekitRoom.prepareConnection(data.data.url, data.data.access_token);\n            updateStatus(\"Connection prepared successfully\", \"success\");\n            await connectWebSocket(data.data.session_id, token);\n            updateStatus(\"Session created successfully\", \"success\");\n            // Return both session data, token, and room for immediate use\n            return {\n                sessionData: data.data,\n                token,\n                room: livekitRoom\n            };\n        } catch (error) {\n            updateStatus(\"Failed to create session: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Start streaming session\n    const startStreamingSession = async (sessionData, token, livekitRoom)=>{\n        if (!sessionData || !sessionData.session_id) {\n            throw new Error('No session info available');\n        }\n        try {\n            updateStatus(\"Debug: Using token: \".concat(token ? 'Token exists' : 'No token'), \"info\");\n            updateStatus(\"Debug: Session ID: \".concat(sessionData.session_id), \"info\");\n            const startResponse = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.start\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    session_id: sessionData.session_id\n                })\n            });\n            updateStatus(\"Debug: Start response status: \".concat(startResponse.status), \"info\");\n            if (!startResponse.ok) {\n                const errorText = await startResponse.text();\n                updateStatus(\"Debug: Error response: \".concat(errorText), \"error\");\n                throw new Error(\"HTTP error! status: \".concat(startResponse.status, \" - \").concat(errorText));\n            }\n            // Connect to LiveKit room\n            updateStatus(\"Debug: Available session properties: \".concat(Object.keys(sessionData).join(', ')), \"info\");\n            updateStatus(\"Debug: Room exists: \".concat(!!livekitRoom), \"info\");\n            updateStatus(\"Debug: URL exists: \".concat(!!sessionData.url), \"info\");\n            updateStatus(\"Debug: Access token exists: \".concat(!!sessionData.access_token), \"info\");\n            if (livekitRoom && sessionData.url && sessionData.access_token) {\n                await livekitRoom.connect(sessionData.url, sessionData.access_token);\n                updateStatus(\"Connected to room successfully\", \"success\");\n            } else {\n                updateStatus(\"Warning: Room or connection details missing\", \"warning\");\n                updateStatus(\"Debug: Missing - Room: \".concat(!livekitRoom, \", URL: \").concat(!sessionData.url, \", Token: \").concat(!sessionData.access_token), \"error\");\n            }\n            setIsStarted(true);\n            updateStatus(\"Streaming started successfully\", \"success\");\n        } catch (error) {\n            updateStatus(\"Failed to start streaming: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Send text to avatar\n    const sendText = async function(text) {\n        let taskType = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"talk\";\n        if (!sessionInfo) {\n            updateStatus(\"No active session - please start the avatar first\", \"warning\");\n            return;\n        }\n        const token = sessionToken;\n        if (!token) {\n            updateStatus(\"No session token available\", \"error\");\n            return;\n        }\n        try {\n            updateStatus(\"Sending message to avatar...\", \"info\");\n            const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.task\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    session_id: sessionInfo.session_id,\n                    text: text,\n                    task_type: taskType\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || data.error) {\n                updateStatus(\"HeyGen API error: \".concat(data.error || response.statusText), \"error\");\n            } else {\n                updateStatus(\"✓ Message sent successfully (\".concat(taskType, \")\"), \"success\");\n                updateStatus('Avatar will speak: \"'.concat(text.substring(0, 50)).concat(text.length > 50 ? '...' : '', '\"'), \"info\");\n            }\n        } catch (err) {\n            updateStatus(\"HeyGen API call failed: \" + err.message, \"error\");\n        }\n    };\n    // Close session\n    const closeSession = async ()=>{\n        if (!sessionInfo) {\n            updateStatus(\"No active session\", \"warning\");\n            return;\n        }\n        try {\n            const token = sessionToken;\n            if (token) {\n                const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.stop\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        Authorization: \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        session_id: sessionInfo.session_id\n                    })\n                });\n            }\n            // Close WebSocket\n            if (webSocket) {\n                webSocket.close();\n            }\n            // Disconnect from LiveKit room\n            if (room) {\n                room.disconnect();\n            }\n            // Reset states\n            if (mediaElementRef.current) {\n                mediaElementRef.current.srcObject = null;\n            }\n            setSessionInfo(null);\n            setRoom(null);\n            setMediaStream(null);\n            setSessionToken(null);\n            setIsStarted(false);\n            updateStatus(\"Session closed successfully\", \"warning\");\n        } catch (error) {\n            updateStatus(\"Error closing session: \".concat(error.message), \"error\");\n            // Still reset states even if API call fails\n            setSessionInfo(null);\n            setRoom(null);\n            setMediaStream(null);\n            setSessionToken(null);\n            setIsStarted(false);\n        }\n    };\n    // Azure Search and OpenAI integration\n    const callAzureOpenAI = async (prompt)=>{\n        try {\n            var _data_choices__message, _data_choices_, _data_choices;\n            const response = await fetch(\"http://localhost:5001/api/openai\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    prompt\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || !data.choices) {\n                const errorMsg = data.error ? typeof data.error === 'object' ? JSON.stringify(data.error) : data.error : response.statusText;\n                updateStatus(\"Azure OpenAI error: \" + errorMsg, \"error\");\n                return null;\n            }\n            return (_data_choices = data.choices) === null || _data_choices === void 0 ? void 0 : (_data_choices_ = _data_choices[0]) === null || _data_choices_ === void 0 ? void 0 : (_data_choices__message = _data_choices_.message) === null || _data_choices__message === void 0 ? void 0 : _data_choices__message.content;\n        } catch (err) {\n            updateStatus(\"Azure OpenAI call failed: \" + err.message, \"error\");\n            return null;\n        }\n    };\n    const searchAzure = async (query)=>{\n        try {\n            const response = await fetch(\"http://localhost:5001/api/search\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    query,\n                    courseId\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || !data.value) {\n                const errorMsg = data.error ? typeof data.error === 'object' ? JSON.stringify(data.error) : data.error : response.statusText;\n                updateStatus(\"Azure Search error: \" + errorMsg, \"error\");\n                return null;\n            }\n            updateStatus(\"Found \".concat(data.value.length, \" relevant documents\"), \"success\");\n            return (data.value || []).map((r)=>r.content).join('\\n\\n');\n        } catch (err) {\n            updateStatus(\"Azure Search call failed: \" + err.message, \"error\");\n            return null;\n        }\n    };\n    // Handle Ask AI button\n    const handleAskAI = async ()=>{\n        const text = taskInput.trim();\n        if (!text) {\n            updateStatus(\"Please enter a question first\", \"warning\");\n            return;\n        }\n        if (!sessionInfo) {\n            updateStatus(\"Please start the avatar session first\", \"warning\");\n            return;\n        }\n        try {\n            updateStatus('\\uD83D\\uDD0D Searching for: \"'.concat(text, '\"'), \"info\");\n            const searchResults = await searchAzure(text);\n            if (!searchResults) {\n                updateStatus(\"No relevant documents found for your question\", \"warning\");\n                return;\n            }\n            updateStatus(\"🤖 Processing with AI...\", \"info\");\n            const llmAnswer = await callAzureOpenAI('Based on the following context, answer the question: \"'.concat(text, '\"\\n\\nContext: ').concat(searchResults));\n            if (llmAnswer) {\n                updateStatus(\"\\uD83D\\uDCA1 AI Response: \".concat(llmAnswer.substring(0, 100)).concat(llmAnswer.length > 100 ? '...' : ''), \"success\");\n                // Clean the response for avatar speech\n                let cleaned = llmAnswer.replace(/[#*_`>-]+/g, '').replace(/\\n{2,}/g, ' ').replace(/\\s{2,}/g, ' ').trim();\n                updateStatus(\"\\uD83C\\uDFA4 Avatar speaking response...\", \"info\");\n                await sendText(cleaned, \"repeat\");\n                setTaskInput(\"\");\n            } else {\n                updateStatus(\"Failed to generate AI response\", \"error\");\n            }\n        } catch (error) {\n            updateStatus(\"Error processing question: \".concat(error.message), \"error\");\n        }\n    };\n    // Handle Voice Input button\n    const handleVoiceInput = ()=>{\n        if (!isVoiceSupported) {\n            updateStatus(\"Voice input not supported in this browser\", \"warning\");\n            return;\n        }\n        if (!recognition) {\n            updateStatus(\"Voice recognition not initialized\", \"error\");\n            return;\n        }\n        if (isRecording) {\n            // Stop recording\n            recognition.stop();\n            setIsRecording(false);\n            updateStatus(\"Voice recording stopped\", \"info\");\n        } else {\n            // Start recording\n            try {\n                recognition.start();\n                setIsRecording(true);\n                updateStatus(\"🎤 Listening... Speak your question\", \"info\");\n            } catch (error) {\n                updateStatus(\"Failed to start voice recording: \".concat(error.message), \"error\");\n                setIsRecording(false);\n            }\n        }\n    };\n    // Handle Start button\n    const handleStart = async ()=>{\n        try {\n            const result = await createNewSession();\n            await startStreamingSession(result.sessionData, result.token, result.room);\n        } catch (error) {\n            updateStatus(\"Error starting session: \".concat(error.message), \"error\");\n        }\n    };\n    // Handle Enter key in textarea\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleAskAI();\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-white\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold\",\n                                                children: \"AI Avatar Assistant\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white text-opacity-70 text-sm\",\n                                                children: \"Powered by EXL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full \".concat(isStarted ? 'bg-green-400 animate-pulse' : 'bg-gray-400')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-opacity-80 text-sm font-medium\",\n                                                children: isStarted ? 'Live' : 'Offline'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        className: \"text-white hover:text-gray-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                    lineNumber: 592,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 max-h-[calc(90vh-120px)] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-2xl p-6 border border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800 mb-6 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2 text-blue-600\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 633,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Avatar Configuration\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 631,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Avatar ID\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 641,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: \"Enter Avatar ID\",\n                                                                value: avatarID,\n                                                                onChange: (e)=>setAvatarID(e.target.value),\n                                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 642,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 640,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-3 mt-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleStart,\n                                                                disabled: isStarted,\n                                                                className: \"px-4 py-3 bg-green-500 hover:bg-green-600 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none shadow-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 inline mr-2\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 670,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 669,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    isStarted ? 'Connected' : 'Start'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 664,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: closeSession,\n                                                                className: \"px-4 py-3 bg-red-500 hover:bg-red-600 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 inline mr-2\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 679,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 678,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \"Stop\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 674,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 630,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-2xl p-6 border border-gray-200 mt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-800 mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2 text-cyan-600\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Ask Your Question\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 689,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            placeholder: \"Type your question here...\",\n                                                            value: taskInput,\n                                                            onChange: (e)=>setTaskInput(e.target.value),\n                                                            onKeyDown: handleKeyDown,\n                                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-200 resize-none h-24\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 698,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 697,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleAskAI,\n                                                                className: \"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 inline mr-2\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 713,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 712,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \"Ask AI\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 708,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleVoiceInput,\n                                                                disabled: !isVoiceSupported,\n                                                                className: \"px-4 py-3 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg \".concat(isRecording ? 'bg-red-500 hover:bg-red-600 animate-pulse' : isVoiceSupported ? 'bg-purple-500 hover:bg-purple-600' : 'bg-gray-400 cursor-not-allowed'),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 inline mr-2\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a2 2 0 114 0v4a2 2 0 11-4 0V7z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 730,\n                                                                            columnNumber: 27\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 732,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 728,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    isRecording ? 'Stop Recording' : 'Voice Input'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 717,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 707,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 688,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-2xl p-6 border border-gray-200 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-semibold text-gray-800 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 mr-2 text-blue-600\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M2 6a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 749,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 748,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"AI Avatar\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 747,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 754,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Ready\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 755,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 746,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                ref: mediaElementRef,\n                                                className: \"w-full h-96 bg-gray-900 rounded-xl object-cover shadow-lg\",\n                                                autoPlay: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-16 h-16 mx-auto mb-4 opacity-50\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                    lineNumber: 768,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 767,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-medium\",\n                                                                children: \"Avatar will appear here\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 770,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm opacity-75\",\n                                                                children: \"Click Start to begin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 771,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 766,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 760,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 759,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                    lineNumber: 745,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 743,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                        lineNumber: 626,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                    lineNumber: 625,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n            lineNumber: 590,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n        lineNumber: 589,\n        columnNumber: 5\n    }, undefined);\n};\n_s(QnAWithHeyGenModal, \"sQLL3/36pfSxc1T2aOA1CeM+JLY=\");\n_c = QnAWithHeyGenModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QnAWithHeyGenModal);\nvar _c;\n$RefreshReg$(_c, \"QnAWithHeyGenModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/QnAWithHeyGenModal.tsx\n"));

/***/ })

});