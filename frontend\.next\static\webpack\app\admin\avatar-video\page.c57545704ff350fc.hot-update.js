"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/avatar-video/page",{

/***/ "(app-pages-browser)/./src/app/admin/AvatarVideoPanel.tsx":
/*!********************************************!*\
  !*** ./src/app/admin/AvatarVideoPanel.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AvatarVideoPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clapperboard.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-user-round.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction AvatarVideoPanel() {\n    var _avatars_find, _uniqueCourses_find, _filteredVersions_find;\n    _s();\n    const [avatars, setAvatars] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]);\n    const [selectedAvatarId, setSelectedAvatarId] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null);\n    const [approvedVersions, setApprovedVersions] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]);\n    const [selectedCourseIdForVideo, setSelectedCourseIdForVideo] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null);\n    const [selectedVersionId, setSelectedVersionId] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null);\n    const [loadingVersions, setLoadingVersions] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(true);\n    const [loadingAvatars, setLoadingAvatars] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(true);\n    const [generatingVideos, setGeneratingVideos] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false);\n    const [videoGenerationResults, setVideoGenerationResults] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]);\n    const [showConfirmDialog, setShowConfirmDialog] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false);\n    const [generationProgress, setGenerationProgress] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)({\n        current: 0,\n        total: 0\n    });\n    const [currentGeneratingPage, setCurrentGeneratingPage] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)('');\n    // Auto-generation states\n    const [autoStartCountdown, setAutoStartCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null);\n    const [hasAutoStarted, setHasAutoStarted] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false);\n    const countdownIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_8__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_8__.useEffect)({\n        \"AvatarVideoPanel.useEffect\": ()=>{\n            const fetchApprovedVersions = {\n                \"AvatarVideoPanel.useEffect.fetchApprovedVersions\": async ()=>{\n                    try {\n                        const res = await fetch(\"http://localhost:5001/api/all-approved-content-versions\");\n                        const data = await res.json();\n                        setApprovedVersions(data);\n                    } catch (err) {\n                        console.error(\"Failed to fetch approved versions:\", err);\n                        alert(\"Failed to load approved content versions.\");\n                    } finally{\n                        setLoadingVersions(false);\n                    }\n                }\n            }[\"AvatarVideoPanel.useEffect.fetchApprovedVersions\"];\n            const fetchAvatars = {\n                \"AvatarVideoPanel.useEffect.fetchAvatars\": async ()=>{\n                    try {\n                        const res = await fetch(\"http://localhost:5001/api/avatars\");\n                        const data = await res.json();\n                        if (Array.isArray(data)) {\n                            setAvatars(data);\n                        } else {\n                            console.error(\"API returned non-array data for avatars:\", data);\n                            setAvatars([]);\n                            alert(\"Failed to load avatar configurations: Invalid data format.\");\n                        }\n                    } catch (err) {\n                        console.error(\"Failed to fetch avatars:\", err);\n                        alert(\"Failed to load avatar configurations. Please ensure backend is running.\");\n                    } finally{\n                        setLoadingAvatars(false);\n                    }\n                }\n            }[\"AvatarVideoPanel.useEffect.fetchAvatars\"];\n            fetchApprovedVersions();\n            fetchAvatars();\n        }\n    }[\"AvatarVideoPanel.useEffect\"], []);\n    // Auto-generation effect - triggers when both avatar and version are selected\n    (0,react__WEBPACK_IMPORTED_MODULE_8__.useEffect)({\n        \"AvatarVideoPanel.useEffect\": ()=>{\n            // Clear any existing countdown\n            if (countdownIntervalRef.current) {\n                clearInterval(countdownIntervalRef.current);\n                countdownIntervalRef.current = null;\n            }\n            // Only auto-start if both selections are made and we haven't already auto-started\n            if (selectedAvatarId && selectedVersionId && !hasAutoStarted && !generatingVideos && videoGenerationResults.length === 0) {\n                console.log(\"Both avatar and version selected, starting auto-generation countdown...\");\n                // Start countdown from 3 seconds\n                setAutoStartCountdown(3);\n                countdownIntervalRef.current = setInterval({\n                    \"AvatarVideoPanel.useEffect\": ()=>{\n                        setAutoStartCountdown({\n                            \"AvatarVideoPanel.useEffect\": (prev)=>{\n                                if (prev === null || prev <= 1) {\n                                    // Countdown finished, start generation\n                                    if (countdownIntervalRef.current) {\n                                        clearInterval(countdownIntervalRef.current);\n                                        countdownIntervalRef.current = null;\n                                    }\n                                    setHasAutoStarted(true);\n                                    setAutoStartCountdown(null);\n                                    // Trigger generation directly without confirmation dialog\n                                    handleConfirmGeneration();\n                                    return null;\n                                }\n                                return prev - 1;\n                            }\n                        }[\"AvatarVideoPanel.useEffect\"]);\n                    }\n                }[\"AvatarVideoPanel.useEffect\"], 1000);\n            } else {\n                setAutoStartCountdown(null);\n            }\n            // Cleanup interval on unmount or dependency change\n            return ({\n                \"AvatarVideoPanel.useEffect\": ()=>{\n                    if (countdownIntervalRef.current) {\n                        clearInterval(countdownIntervalRef.current);\n                        countdownIntervalRef.current = null;\n                    }\n                }\n            })[\"AvatarVideoPanel.useEffect\"];\n        }\n    }[\"AvatarVideoPanel.useEffect\"], [\n        selectedAvatarId,\n        selectedVersionId,\n        hasAutoStarted,\n        generatingVideos,\n        videoGenerationResults.length\n    ]);\n    // Reset auto-start flag when selections change\n    (0,react__WEBPACK_IMPORTED_MODULE_8__.useEffect)({\n        \"AvatarVideoPanel.useEffect\": ()=>{\n            if (!selectedAvatarId || !selectedVersionId) {\n                setHasAutoStarted(false);\n                setAutoStartCountdown(null);\n            }\n        }\n    }[\"AvatarVideoPanel.useEffect\"], [\n        selectedAvatarId,\n        selectedVersionId\n    ]);\n    const cancelAutoStart = ()=>{\n        if (countdownIntervalRef.current) {\n            clearInterval(countdownIntervalRef.current);\n            countdownIntervalRef.current = null;\n        }\n        setAutoStartCountdown(null);\n        setHasAutoStarted(true); // Prevent auto-start from happening again\n    };\n    const uniqueCourses = approvedVersions.reduce((acc, version)=>{\n        if (!acc.find((course)=>course.course_id === version.course_id)) {\n            acc.push({\n                course_id: version.course_id,\n                course_title: version.course_title\n            });\n        }\n        return acc;\n    }, []);\n    const filteredVersions = approvedVersions.filter((version)=>version.course_id === Number(selectedCourseIdForVideo));\n    const handleGenerateVideosClick = ()=>{\n        if (!selectedAvatarId || !selectedVersionId) {\n            alert(\"Please select both an avatar and a content version.\");\n            return;\n        }\n        setShowConfirmDialog(true);\n    };\n    const handleConfirmGeneration = async ()=>{\n        setShowConfirmDialog(false);\n        setGeneratingVideos(true);\n        setVideoGenerationResults([]);\n        setGenerationProgress({\n            current: 0,\n            total: 0\n        });\n        setCurrentGeneratingPage('');\n        try {\n            // First get the content to know total pages\n            const versionRes = await fetch(\"http://localhost:5001/api/course-content-versions/\".concat(selectedVersionId));\n            const versionData = await versionRes.json();\n            const totalPages = Array.isArray(versionData.content) ? versionData.content.length : 0;\n            setGenerationProgress({\n                current: 0,\n                total: totalPages\n            });\n            const res = await fetch(\"http://localhost:5001/api/generate-course-videos\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    selectedAvatarId,\n                    selectedVersionId\n                })\n            });\n            const result = await res.json();\n            if (!res.ok) {\n                throw new Error(result.error || \"Failed to initiate video generation\");\n            }\n            console.log(\"Generated Videos Data:\", result.generated_videos);\n            setVideoGenerationResults(result.generated_videos || []);\n        } catch (error) {\n            console.error(\"Video generation error:\", error);\n            alert(\"Video generation failed: \".concat(error.message || \"Unknown error\"));\n        } finally{\n            setGeneratingVideos(false);\n            setCurrentGeneratingPage('');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"mb-8 border-0 shadow-xl bg-white/80 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20 h-20 bg-orange-400 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-10 h-10 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold mb-3 text-gray-900 bg-brand-blue bg-clip-text text-transparent\",\n                                    children: \"Medical Course Avatar & Video Generation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n                                    children: \"Select a medical professional avatar and generate engaging videos from your approved summaries with cutting-edge AI technology\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {\n                            className: \"my-8\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"p-4 transition-all duration-300 \".concat(selectedAvatarId ? 'border-blue-500 bg-blue-50 shadow-lg scale-105' : 'border-gray-200 hover:shadow-md'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-xl flex items-center justify-center transition-colors \".concat(selectedAvatarId ? 'bg-blue-500' : 'bg-gray-200'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-6 h-6 \".concat(selectedAvatarId ? 'text-white' : 'text-gray-400')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold \".concat(selectedAvatarId ? 'text-blue-600' : 'text-gray-500'),\n                                                        children: \"Select Avatar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Choose your presenter\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"p-4 transition-all duration-300 \".concat(selectedVersionId ? 'border-green-500 bg-green-50 shadow-lg scale-105' : 'border-gray-200 hover:shadow-md'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-xl flex items-center justify-center transition-colors \".concat(selectedVersionId ? 'bg-green-500' : 'bg-gray-200'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-6 h-6 \".concat(selectedVersionId ? 'text-white' : 'text-gray-400')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold \".concat(selectedVersionId ? 'text-green-600' : 'text-gray-500'),\n                                                        children: \"Select Content\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Choose course material\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"p-4 transition-all duration-300 \".concat(generatingVideos ? 'border-amber-500 bg-amber-50 shadow-lg scale-105' : 'border-gray-200 hover:shadow-md'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-xl flex items-center justify-center transition-colors \".concat(generatingVideos ? 'bg-amber-500' : 'bg-gray-200'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-6 h-6 \".concat(generatingVideos ? 'text-white' : 'text-gray-400')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold \".concat(generatingVideos ? 'text-amber-600' : 'text-gray-500'),\n                                                        children: \"Generate Videos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Create content\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"mb-8 border-0 shadow-xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        className: \"pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-7 h-7 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"text-2xl text-gray-900\",\n                                            children: \"Select Medical Professional Avatar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mt-1\",\n                                            children: \"Choose your video presenter from our collection of medical professionals\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loadingAvatars ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"Loading avatars...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this) : avatars.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"p-12 text-center bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDC65\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 text-lg\",\n                                    children: \"No avatars available.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                            children: avatars.map((a)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"group relative cursor-pointer transition-all duration-300 hover:shadow-xl \".concat(selectedAvatarId === a.id ? \"border-2 border-blue-500 bg-blue-50 shadow-xl scale-105\" : \"border hover:border-blue-300 hover:bg-blue-50/50 hover:scale-102\"),\n                                    onClick: ()=>setSelectedAvatarId(a.id),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"p-6 text-center\",\n                                        style: {\n                                            minHeight: 280\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center transition-all \".concat(selectedAvatarId === a.id ? 'bg-blue-500 shadow-lg' : 'bg-orange-100 group-hover:bg-blue-100'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-8 h-8 transition-colors \".concat(selectedAvatarId === a.id ? 'text-white' : 'text-orange-600 group-hover:text-blue-600')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-gray-900 mb-3 text-lg truncate\",\n                                                title: a.avatar_name,\n                                                children: a.avatar_name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"text-xs\",\n                                                        children: a.specialty || \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500 truncate\",\n                                                        title: a.domain || \"N/A\",\n                                                        children: a.domain || \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-orange-600 truncate\",\n                                                        title: a.voice_id || \"N/A\",\n                                                        children: [\n                                                            \"Voice: \",\n                                                            a.voice_id || \"N/A\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: selectedAvatarId === a.id ? \"default\" : \"outline\",\n                                                className: \"w-full transition-all duration-200 \".concat(selectedAvatarId === a.id ? \"bg-blue-600 hover:bg-blue-700 text-white shadow-md\" : \"hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300\"),\n                                                children: selectedAvatarId === a.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        \"Selected\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 25\n                                                }, this) : \"Select Avatar\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 19\n                                    }, this)\n                                }, a.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this),\n            selectedAvatarId !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"mb-8 border-0 shadow-xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        className: \"pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-7 h-7 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"text-2xl text-gray-900\",\n                                            children: \"Select Course Content for Video Generation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mt-1\",\n                                            children: \"Choose your course and approved version for video creation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loadingVersions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-2 border-gray-300 border-t-green-500 rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"Loading approved content versions...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 15\n                        }, this) : approvedVersions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"p-12 text-center bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDCDA\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 text-lg\",\n                                    children: \"No approved content versions available.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_2__.Label, {\n                                            htmlFor: \"course\",\n                                            className: \"text-base font-semibold text-gray-700\",\n                                            children: \"Course Selection\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                            value: selectedCourseIdForVideo || '',\n                                            onValueChange: (value)=>{\n                                                setSelectedCourseIdForVideo(value);\n                                                setSelectedVersionId(null);\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                                    className: \"w-full h-14 text-base focus:ring-2 focus:ring-green-200 border-2 rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                                        placeholder: \"Choose a course to generate videos from\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                                    children: uniqueCourses.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                            value: course.course_id.toString(),\n                                                            className: \"text-base py-3\",\n                                                            children: course.course_title\n                                                        }, course.course_id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 17\n                                }, this),\n                                selectedCourseIdForVideo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_2__.Label, {\n                                            htmlFor: \"version\",\n                                            className: \"text-base font-semibold text-gray-700\",\n                                            children: \"Version Selection\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                            value: selectedVersionId || '',\n                                            onValueChange: (value)=>{\n                                                setSelectedVersionId(value);\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                                    className: \"w-full h-14 text-base focus:ring-2 focus:ring-green-200 border-2 rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                                        placeholder: \"Select an approved version\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                                    children: filteredVersions.map((version)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                            value: version.version_id.toString(),\n                                                            className: \"text-base py-3\",\n                                                            children: [\n                                                                \"Version \",\n                                                                version.version_number,\n                                                                \" (Approved: \",\n                                                                new Date(version.approved_at).toLocaleDateString(),\n                                                                \")\"\n                                                            ]\n                                                        }, version.version_id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 346,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"border-0 shadow-xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        className: \"pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-amber-100 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-7 h-7 text-amber-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"text-2xl text-gray-900\",\n                                                    children: \"Video Generation Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mt-1\",\n                                                    children: \"Generate professional video content from your selections\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: 'warning',\n                                    size: \"lg\",\n                                    className: \"flex items-center space-x-3 px-8 py-4 text-white font-semibold rounded-2xl shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105\",\n                                    onClick: handleGenerateVideosClick,\n                                    disabled: !selectedAvatarId || !selectedVersionId || generatingVideos,\n                                    children: generatingVideos ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"animate-spin w-6 h-6 text-white\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                        className: \"opacity-25\",\n                                                        cx: \"12\",\n                                                        cy: \"12\",\n                                                        r: \"10\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        className: \"opacity-75\",\n                                                        fill: \"currentColor\",\n                                                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: \"Generating Videos...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: \"Generate All Videos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: [\n                            generatingVideos && generationProgress.total > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"mb-8 bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-blue-900 text-lg flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-5 h-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Generation Progress\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"bg-blue-100 text-blue-800 font-semibold\",\n                                                    children: [\n                                                        generationProgress.current,\n                                                        \" of \",\n                                                        generationProgress.total,\n                                                        \" completed\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_6__.Progress, {\n                                            value: generationProgress.current / generationProgress.total * 100,\n                                            className: \"h-3 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 17\n                                        }, this),\n                                        currentGeneratingPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-700 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Currently generating: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium ml-1\",\n                                                    children: currentGeneratingPage\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 43\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 13\n                            }, this),\n                            videoGenerationResults.length === 0 && !generatingVideos && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-32 h-32 bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-16 h-16 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                        children: \"Ready to Generate Videos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 max-w-lg mx-auto mb-8 text-lg leading-relaxed\",\n                                        children: 'Select an avatar and course content, then click \"Generate All Videos\" to begin creating your professional video content with AI.'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 15\n                                    }, this),\n                                    (!selectedAvatarId || !selectedVersionId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-200 max-w-md mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2 text-amber-700 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-lg\",\n                                                            children: \"Selection Required\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-amber-600\",\n                                                    children: \"Please select both an avatar and course content to proceed with video generation.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 17\n                                    }, this),\n                                    selectedAvatarId && selectedVersionId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200 max-w-md mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2 text-green-700 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-lg\",\n                                                            children: \"Ready to Generate!\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-green-600\",\n                                                    children: \"Avatar and content selected. Click the button above to start video generation.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 491,\n                                columnNumber: 13\n                            }, this),\n                            videoGenerationResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-6 h-6 text-gray-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-bold text-gray-800 text-xl\",\n                                                children: \"Generated Videos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"bg-green-100 text-green-800\",\n                                                children: [\n                                                    videoGenerationResults.length,\n                                                    \" videos processed\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-4\",\n                                        children: videoGenerationResults.map((page, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                className: \"hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center mb-4\",\n                                                                    children: [\n                                                                        page.generation_status === \"success\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 bg-green-500 rounded-full mr-4 flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-white\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                                lineNumber: 549,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 548,\n                                                                            columnNumber: 31\n                                                                        }, this) : page.generation_status === \"skipped_empty_script\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 bg-yellow-500 rounded-full mr-4 flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-white\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                                lineNumber: 553,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 552,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 bg-red-500 rounded-full mr-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 556,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                            className: \"font-bold text-gray-800 text-xl\",\n                                                                            children: page.title || \"Chapter \".concat(page.page)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 558,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"mb-4\",\n                                                                    children: [\n                                                                        \"Page \",\n                                                                        page.page\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 562,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                page.generation_status === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: \"bg-green-100 text-green-800 hover:bg-green-100\",\n                                                                    children: \"✅ Generated Successfully\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                page.generation_status === \"skipped_empty_script\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: \"bg-yellow-100 text-yellow-800 hover:bg-yellow-100\",\n                                                                    children: \"⚠️ Skipped (Empty Script)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 573,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                page.generation_status === \"failed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            className: \"bg-red-100 text-red-800 hover:bg-red-100\",\n                                                                            children: \"❌ Generation Failed\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 580,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-red-600 mt-2 p-3 bg-red-50 rounded-lg border border-red-200\",\n                                                                            children: page.error_details || \"Unknown error occurred during generation\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 583,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 579,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 424,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: showConfirmDialog,\n                onOpenChange: setShowConfirmDialog,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    className: \"max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20 h-20 bg-gradient-to-br from-amber-400 to-orange-500 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-10 h-10 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 603,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                    className: \"text-2xl font-bold text-gray-800 mb-4\",\n                                    children: \"Generate All Videos?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 606,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-6 text-lg leading-relaxed\",\n                                        children: [\n                                            \"This will generate videos for all pages in the selected course content.\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 88\n                                            }, this),\n                                            \"This process may take several minutes to complete.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 602,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-200 mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center font-bold mb-4 text-amber-900 text-lg\",\n                                        children: \"Selected Configuration\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-6 h-6 text-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-amber-800\",\n                                                            children: \"Avatar\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 622,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-amber-700 break-words\",\n                                                            children: (_avatars_find = avatars.find((a)=>a.id === selectedAvatarId)) === null || _avatars_find === void 0 ? void 0 : _avatars_find.avatar_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 619,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-6 h-6 text-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-amber-800\",\n                                                            children: \"Course\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-amber-700 break-words\",\n                                                            children: (_uniqueCourses_find = uniqueCourses.find((c)=>c.course_id === Number(selectedCourseIdForVideo))) === null || _uniqueCourses_find === void 0 ? void 0 : _uniqueCourses_find.course_title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-6 h-6 text-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-amber-800\",\n                                                            children: \"Version\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 640,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-amber-700 break-words\",\n                                                            children: (_filteredVersions_find = filteredVersions.find((v)=>v.version_id === selectedVersionId)) === null || _filteredVersions_find === void 0 ? void 0 : _filteredVersions_find.version_number\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 641,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 616,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 615,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogFooter, {\n                            className: \"flex space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"outline\",\n                                    className: \"flex-1 h-12 text-base\",\n                                    onClick: ()=>setShowConfirmDialog(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 651,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"brand\",\n                                    className: \"flex-1 h-12 text-base\",\n                                    onClick: handleConfirmGeneration,\n                                    children: \"Yes, Generate Videos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 650,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                    lineNumber: 601,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 600,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n        lineNumber: 193,\n        columnNumber: 5\n    }, this);\n}\n_s(AvatarVideoPanel, \"cQda4cTiyx88ziR/zLX0nTddeMI=\");\n_c = AvatarVideoPanel;\nvar _c;\n$RefreshReg$(_c, \"AvatarVideoPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/AvatarVideoPanel.tsx\n"));

/***/ })

});