"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/courses/[id]/page",{

/***/ "(app-pages-browser)/./src/components/QnAWithHeyGenModal.tsx":
/*!***********************************************!*\
  !*** ./src/components/QnAWithHeyGenModal.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var livekit_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! livekit-client */ \"(app-pages-browser)/./node_modules/livekit-client/dist/livekit-client.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst QnAWithHeyGenModal = (param)=>{\n    let { isOpen, onClose, courseId } = param;\n    _s();\n    // Configuration\n    const API_CONFIG = {\n        apiKey: \"NDM2Y2M0NDI0YTMzNGM1OWFkZThhZTRmMjJhZmEzNzYtMTc0NzA1OTcyOA==\",\n        serverUrl: \"https://api.heygen.com\"\n    };\n    // State variables\n    const [sessionInfo, setSessionInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sessionToken, setSessionToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [room, setRoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mediaStream, setMediaStream] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [webSocket, setWebSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // const [avatarID, setAvatarID] = useState('Wayne_20240711');\n    const [avatarID, setAvatarID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Pedro_Chair_Sitting_public');\n    const [voiceID, setVoiceID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [taskInput, setTaskInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusMessages, setStatusMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isStarted, setIsStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Voice input states\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recognition, setRecognition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isVoiceSupported, setIsVoiceSupported] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const mediaElementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const statusElementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Helper function to update status with enhanced styling\n    const updateStatus = function(message) {\n        let type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'info';\n        const timestamp = new Date().toLocaleTimeString();\n        const newMessage = {\n            message,\n            type,\n            timestamp\n        };\n        setStatusMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n    };\n    // Auto-scroll status to bottom\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QnAWithHeyGenModal.useEffect\": ()=>{\n            if (statusElementRef.current) {\n                statusElementRef.current.scrollTop = statusElementRef.current.scrollHeight;\n            }\n        }\n    }[\"QnAWithHeyGenModal.useEffect\"], [\n        statusMessages\n    ]);\n    // Initialize status messages and auto-start avatar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QnAWithHeyGenModal.useEffect\": ()=>{\n            if (isOpen) {\n                setStatusMessages([\n                    {\n                        message: \"Welcome to AI Avatar Assistant!\",\n                        type: \"system\",\n                        timestamp: new Date().toLocaleTimeString()\n                    },\n                    {\n                        message: \"Auto-starting avatar session...\",\n                        type: \"info\",\n                        timestamp: new Date().toLocaleTimeString()\n                    },\n                    {\n                        message: \"Please wait while we initialize your AI assistant\",\n                        type: \"info\",\n                        timestamp: new Date().toLocaleTimeString()\n                    }\n                ]);\n                // Auto-start the avatar session after a brief delay to ensure component is ready\n                const autoStartTimer = setTimeout({\n                    \"QnAWithHeyGenModal.useEffect.autoStartTimer\": async ()=>{\n                        try {\n                            await handleStart();\n                        } catch (error) {\n                            console.error(\"Auto-start failed:\", error);\n                        }\n                    }\n                }[\"QnAWithHeyGenModal.useEffect.autoStartTimer\"], 1000); // 1 second delay\n                // Cleanup timer if component unmounts or modal closes\n                return ({\n                    \"QnAWithHeyGenModal.useEffect\": ()=>{\n                        clearTimeout(autoStartTimer);\n                    }\n                })[\"QnAWithHeyGenModal.useEffect\"];\n            }\n        }\n    }[\"QnAWithHeyGenModal.useEffect\"], [\n        isOpen\n    ]);\n    // Initialize voice recognition\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QnAWithHeyGenModal.useEffect\": ()=>{\n            if (true) {\n                // Check for Web Speech API support\n                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n                if (SpeechRecognition) {\n                    setIsVoiceSupported(true);\n                    const recognitionInstance = new SpeechRecognition();\n                    // Configure recognition\n                    recognitionInstance.continuous = false;\n                    recognitionInstance.interimResults = false;\n                    recognitionInstance.lang = 'en-US';\n                    // Handle results\n                    recognitionInstance.onresult = ({\n                        \"QnAWithHeyGenModal.useEffect\": (event)=>{\n                            const transcript = event.results[0][0].transcript;\n                            setTaskInput(transcript);\n                            updateStatus('\\uD83C\\uDFA4 Voice input: \"'.concat(transcript, '\"'), \"success\");\n                        }\n                    })[\"QnAWithHeyGenModal.useEffect\"];\n                    // Handle errors\n                    recognitionInstance.onerror = ({\n                        \"QnAWithHeyGenModal.useEffect\": (event)=>{\n                            updateStatus(\"Voice recognition error: \".concat(event.error), \"error\");\n                            setIsRecording(false);\n                        }\n                    })[\"QnAWithHeyGenModal.useEffect\"];\n                    // Handle end\n                    recognitionInstance.onend = ({\n                        \"QnAWithHeyGenModal.useEffect\": ()=>{\n                            setIsRecording(false);\n                        }\n                    })[\"QnAWithHeyGenModal.useEffect\"];\n                    setRecognition(recognitionInstance);\n                } else {\n                    setIsVoiceSupported(false);\n                    updateStatus(\"Voice input not supported in this browser\", \"warning\");\n                }\n            }\n        }\n    }[\"QnAWithHeyGenModal.useEffect\"], []);\n    // Get session token\n    const getSessionToken = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.create_token\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"X-Api-Key\": API_CONFIG.apiKey\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            if (!data.data || !data.data.token) {\n                throw new Error('Invalid token data received from server');\n            }\n            setSessionToken(data.data.token);\n            updateStatus(\"Session token obtained successfully\", \"success\");\n            return data.data.token;\n        } catch (error) {\n            updateStatus(\"Failed to get session token: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Connect WebSocket\n    const connectWebSocket = async (sessionId, token)=>{\n        try {\n            if (!token) {\n                throw new Error('No session token available for WebSocket connection');\n            }\n            const params = new URLSearchParams({\n                session_id: sessionId,\n                session_token: token,\n                silence_response: 'false',\n                opening_text: \"Hello, how can I help you?\",\n                stt_language: \"en\"\n            });\n            const wsUrl = \"wss://\".concat(new URL(API_CONFIG.serverUrl).hostname, \"/v1/ws/streaming.chat?\").concat(params);\n            const ws = new WebSocket(wsUrl);\n            setWebSocket(ws);\n            ws.addEventListener(\"message\", (event)=>{\n                const eventData = JSON.parse(event.data);\n                console.log(\"Raw WebSocket event:\", eventData);\n            });\n            ws.addEventListener(\"error\", (error)=>{\n                updateStatus(\"WebSocket connection error\", \"error\");\n                console.error(\"WebSocket error:\", error);\n            });\n            ws.addEventListener(\"open\", ()=>{\n                updateStatus(\"WebSocket connected successfully\", \"success\");\n            });\n        } catch (error) {\n            updateStatus(\"WebSocket connection failed: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Create new session\n    const createNewSession = async ()=>{\n        try {\n            // Always get a fresh token to avoid state timing issues\n            const token = await getSessionToken();\n            updateStatus(\"Debug: Creating session with token: \".concat(token ? 'Token exists' : 'No token'), \"info\");\n            const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.new\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    quality: \"high\",\n                    avatar_name: avatarID,\n                    voice: {\n                        voice_id: voiceID,\n                        rate: 1.0\n                    },\n                    version: \"v2\",\n                    video_encoding: \"H264\"\n                })\n            });\n            updateStatus(\"Debug: Create session response status: \".concat(response.status), \"info\");\n            if (!response.ok) {\n                const errorText = await response.text();\n                updateStatus(\"Debug: Create session error: \".concat(errorText), \"error\");\n                throw new Error(\"HTTP error! status: \".concat(response.status, \" - \").concat(errorText));\n            }\n            const data = await response.json();\n            if (!data.data || !data.data.session_id) {\n                throw new Error('Invalid session data received from server');\n            }\n            updateStatus(\"Debug: Session data keys: \".concat(Object.keys(data.data).join(', ')), \"info\");\n            // Set session info and return it for immediate use\n            setSessionInfo(data.data);\n            // Create LiveKit Room\n            const livekitRoom = new livekit_client__WEBPACK_IMPORTED_MODULE_2__.Room({\n                adaptiveStream: true,\n                dynacast: true,\n                videoCaptureDefaults: {\n                    resolution: livekit_client__WEBPACK_IMPORTED_MODULE_2__.VideoPresets.h720.resolution\n                }\n            });\n            setRoom(livekitRoom);\n            // Handle room events\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.DataReceived, (message)=>{\n                const data = new TextDecoder().decode(message);\n                console.log(\"Room message:\", JSON.parse(data));\n            });\n            // Handle media streams\n            const newMediaStream = new MediaStream();\n            setMediaStream(newMediaStream);\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.TrackSubscribed, (track)=>{\n                if (track.kind === \"video\" || track.kind === \"audio\") {\n                    newMediaStream.addTrack(track.mediaStreamTrack);\n                    if (newMediaStream.getVideoTracks().length > 0 && newMediaStream.getAudioTracks().length > 0) {\n                        if (mediaElementRef.current) {\n                            mediaElementRef.current.srcObject = newMediaStream;\n                            updateStatus(\"Media stream ready - Avatar is live!\", \"success\");\n                        }\n                    }\n                }\n            });\n            // Handle media stream removal\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.TrackUnsubscribed, (track)=>{\n                const mediaTrack = track.mediaStreamTrack;\n                if (mediaTrack) {\n                    newMediaStream.removeTrack(mediaTrack);\n                }\n            });\n            // Handle room connection state changes\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.Disconnected, (reason)=>{\n                updateStatus(\"Room disconnected: \".concat(reason), \"warning\");\n            });\n            await livekitRoom.prepareConnection(data.data.url, data.data.access_token);\n            updateStatus(\"Connection prepared successfully\", \"success\");\n            await connectWebSocket(data.data.session_id, token);\n            updateStatus(\"Session created successfully\", \"success\");\n            // Return both session data, token, and room for immediate use\n            return {\n                sessionData: data.data,\n                token,\n                room: livekitRoom\n            };\n        } catch (error) {\n            updateStatus(\"Failed to create session: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Start streaming session\n    const startStreamingSession = async (sessionData, token, livekitRoom)=>{\n        if (!sessionData || !sessionData.session_id) {\n            throw new Error('No session info available');\n        }\n        try {\n            updateStatus(\"Debug: Using token: \".concat(token ? 'Token exists' : 'No token'), \"info\");\n            updateStatus(\"Debug: Session ID: \".concat(sessionData.session_id), \"info\");\n            const startResponse = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.start\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    session_id: sessionData.session_id\n                })\n            });\n            updateStatus(\"Debug: Start response status: \".concat(startResponse.status), \"info\");\n            if (!startResponse.ok) {\n                const errorText = await startResponse.text();\n                updateStatus(\"Debug: Error response: \".concat(errorText), \"error\");\n                throw new Error(\"HTTP error! status: \".concat(startResponse.status, \" - \").concat(errorText));\n            }\n            // Connect to LiveKit room\n            updateStatus(\"Debug: Available session properties: \".concat(Object.keys(sessionData).join(', ')), \"info\");\n            updateStatus(\"Debug: Room exists: \".concat(!!livekitRoom), \"info\");\n            updateStatus(\"Debug: URL exists: \".concat(!!sessionData.url), \"info\");\n            updateStatus(\"Debug: Access token exists: \".concat(!!sessionData.access_token), \"info\");\n            if (livekitRoom && sessionData.url && sessionData.access_token) {\n                await livekitRoom.connect(sessionData.url, sessionData.access_token);\n                updateStatus(\"Connected to room successfully\", \"success\");\n            } else {\n                updateStatus(\"Warning: Room or connection details missing\", \"warning\");\n                updateStatus(\"Debug: Missing - Room: \".concat(!livekitRoom, \", URL: \").concat(!sessionData.url, \", Token: \").concat(!sessionData.access_token), \"error\");\n            }\n            setIsStarted(true);\n            updateStatus(\"Streaming started successfully\", \"success\");\n        } catch (error) {\n            updateStatus(\"Failed to start streaming: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Send text to avatar\n    const sendText = async function(text) {\n        let taskType = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"talk\";\n        if (!sessionInfo) {\n            updateStatus(\"No active session - please start the avatar first\", \"warning\");\n            return;\n        }\n        const token = sessionToken;\n        if (!token) {\n            updateStatus(\"No session token available\", \"error\");\n            return;\n        }\n        try {\n            updateStatus(\"Sending message to avatar...\", \"info\");\n            const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.task\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    session_id: sessionInfo.session_id,\n                    text: text,\n                    task_type: taskType\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || data.error) {\n                updateStatus(\"HeyGen API error: \".concat(data.error || response.statusText), \"error\");\n            } else {\n                updateStatus(\"✓ Message sent successfully (\".concat(taskType, \")\"), \"success\");\n                updateStatus('Avatar will speak: \"'.concat(text.substring(0, 50)).concat(text.length > 50 ? '...' : '', '\"'), \"info\");\n            }\n        } catch (err) {\n            updateStatus(\"HeyGen API call failed: \" + err.message, \"error\");\n        }\n    };\n    // Close session\n    const closeSession = async ()=>{\n        if (!sessionInfo) {\n            updateStatus(\"No active session\", \"warning\");\n            return;\n        }\n        try {\n            const token = sessionToken;\n            if (token) {\n                const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.stop\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        Authorization: \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        session_id: sessionInfo.session_id\n                    })\n                });\n            }\n            // Close WebSocket\n            if (webSocket) {\n                webSocket.close();\n            }\n            // Disconnect from LiveKit room\n            if (room) {\n                room.disconnect();\n            }\n            // Reset states\n            if (mediaElementRef.current) {\n                mediaElementRef.current.srcObject = null;\n            }\n            setSessionInfo(null);\n            setRoom(null);\n            setMediaStream(null);\n            setSessionToken(null);\n            setIsStarted(false);\n            updateStatus(\"Session closed successfully\", \"warning\");\n        } catch (error) {\n            updateStatus(\"Error closing session: \".concat(error.message), \"error\");\n            // Still reset states even if API call fails\n            setSessionInfo(null);\n            setRoom(null);\n            setMediaStream(null);\n            setSessionToken(null);\n            setIsStarted(false);\n        }\n    };\n    // Azure Search and OpenAI integration\n    const callAzureOpenAI = async (prompt)=>{\n        try {\n            var _data_choices__message, _data_choices_, _data_choices;\n            const response = await fetch(\"http://localhost:5001/api/openai\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    prompt\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || !data.choices) {\n                const errorMsg = data.error ? typeof data.error === 'object' ? JSON.stringify(data.error) : data.error : response.statusText;\n                updateStatus(\"Azure OpenAI error: \" + errorMsg, \"error\");\n                return null;\n            }\n            return (_data_choices = data.choices) === null || _data_choices === void 0 ? void 0 : (_data_choices_ = _data_choices[0]) === null || _data_choices_ === void 0 ? void 0 : (_data_choices__message = _data_choices_.message) === null || _data_choices__message === void 0 ? void 0 : _data_choices__message.content;\n        } catch (err) {\n            updateStatus(\"Azure OpenAI call failed: \" + err.message, \"error\");\n            return null;\n        }\n    };\n    const searchAzure = async (query)=>{\n        try {\n            const response = await fetch(\"http://localhost:5001/api/search\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    query,\n                    courseId\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || !data.value) {\n                const errorMsg = data.error ? typeof data.error === 'object' ? JSON.stringify(data.error) : data.error : response.statusText;\n                updateStatus(\"Azure Search error: \" + errorMsg, \"error\");\n                return null;\n            }\n            updateStatus(\"Found \".concat(data.value.length, \" relevant documents\"), \"success\");\n            return (data.value || []).map((r)=>r.content).join('\\n\\n');\n        } catch (err) {\n            updateStatus(\"Azure Search call failed: \" + err.message, \"error\");\n            return null;\n        }\n    };\n    // Handle Ask AI button\n    const handleAskAI = async ()=>{\n        const text = taskInput.trim();\n        if (!text) {\n            updateStatus(\"Please enter a question first\", \"warning\");\n            return;\n        }\n        if (!sessionInfo) {\n            updateStatus(\"Please start the avatar session first\", \"warning\");\n            return;\n        }\n        try {\n            updateStatus('\\uD83D\\uDD0D Searching for: \"'.concat(text, '\"'), \"info\");\n            const searchResults = await searchAzure(text);\n            if (!searchResults) {\n                updateStatus(\"No relevant documents found for your question\", \"warning\");\n                return;\n            }\n            updateStatus(\"🤖 Processing with AI...\", \"info\");\n            const llmAnswer = await callAzureOpenAI('Based on the following context, answer the question: \"'.concat(text, '\"\\n\\nContext: ').concat(searchResults));\n            if (llmAnswer) {\n                updateStatus(\"\\uD83D\\uDCA1 AI Response: \".concat(llmAnswer.substring(0, 100)).concat(llmAnswer.length > 100 ? '...' : ''), \"success\");\n                // Clean the response for avatar speech\n                let cleaned = llmAnswer.replace(/[#*_`>-]+/g, '').replace(/\\n{2,}/g, ' ').replace(/\\s{2,}/g, ' ').trim();\n                updateStatus(\"\\uD83C\\uDFA4 Avatar speaking response...\", \"info\");\n                await sendText(cleaned, \"repeat\");\n                setTaskInput(\"\");\n            } else {\n                updateStatus(\"Failed to generate AI response\", \"error\");\n            }\n        } catch (error) {\n            updateStatus(\"Error processing question: \".concat(error.message), \"error\");\n        }\n    };\n    // Handle Voice Input button\n    const handleVoiceInput = ()=>{\n        if (!isVoiceSupported) {\n            updateStatus(\"Voice input not supported in this browser\", \"warning\");\n            return;\n        }\n        if (!recognition) {\n            updateStatus(\"Voice recognition not initialized\", \"error\");\n            return;\n        }\n        if (isRecording) {\n            // Stop recording\n            recognition.stop();\n            setIsRecording(false);\n            updateStatus(\"Voice recording stopped\", \"info\");\n        } else {\n            // Start recording\n            try {\n                recognition.start();\n                setIsRecording(true);\n                updateStatus(\"🎤 Listening... Speak your question\", \"info\");\n            } catch (error) {\n                updateStatus(\"Failed to start voice recording: \".concat(error.message), \"error\");\n                setIsRecording(false);\n            }\n        }\n    };\n    // Handle Start button\n    const handleStart = async ()=>{\n        try {\n            const result = await createNewSession();\n            await startStreamingSession(result.sessionData, result.token, result.room);\n        } catch (error) {\n            updateStatus(\"Error starting session: \".concat(error.message), \"error\");\n        }\n    };\n    // Handle Enter key in textarea\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleAskAI();\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-white\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold\",\n                                                children: \"AI Avatar Assistant\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white text-opacity-70 text-sm\",\n                                                children: \"Powered by EXL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full \".concat(isStarted ? 'bg-green-400 animate-pulse' : 'bg-gray-400')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-opacity-80 text-sm font-medium\",\n                                                children: isStarted ? 'Live' : 'Offline'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        className: \"text-white hover:text-gray-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                    lineNumber: 592,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 max-h-[calc(90vh-120px)] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-2xl p-6 border border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800 mb-6 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2 text-blue-600\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 633,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Avatar Configuration\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 631,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Avatar ID\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 641,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: \"Enter Avatar ID\",\n                                                                value: avatarID,\n                                                                onChange: (e)=>setAvatarID(e.target.value),\n                                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 642,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 640,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-3 mt-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleStart,\n                                                                disabled: true,\n                                                                className: \"px-4 py-3 bg-green-500 hover:bg-green-600 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none shadow-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 inline mr-2\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 670,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 669,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    isStarted ? 'Connected' : 'Auto-Starting...'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 664,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: closeSession,\n                                                                className: \"px-4 py-3 bg-red-500 hover:bg-red-600 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 inline mr-2\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 679,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 678,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \"Stop\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 674,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 630,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-2xl p-6 border border-gray-200 mt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-800 mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2 text-cyan-600\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Ask Your Question\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 689,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            placeholder: \"Type your question here...\",\n                                                            value: taskInput,\n                                                            onChange: (e)=>setTaskInput(e.target.value),\n                                                            onKeyDown: handleKeyDown,\n                                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-200 resize-none h-24\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 698,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 697,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleAskAI,\n                                                                className: \"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 inline mr-2\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 713,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 712,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \"Ask AI\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 708,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleVoiceInput,\n                                                                disabled: !isVoiceSupported,\n                                                                className: \"px-4 py-3 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg \".concat(isRecording ? 'bg-red-500 hover:bg-red-600 animate-pulse' : isVoiceSupported ? 'bg-purple-500 hover:bg-purple-600' : 'bg-gray-400 cursor-not-allowed'),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 inline mr-2\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a2 2 0 114 0v4a2 2 0 11-4 0V7z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 730,\n                                                                            columnNumber: 27\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 732,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 728,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    isRecording ? 'Stop Recording' : 'Voice Input'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 717,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 707,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 688,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-2xl p-6 border border-gray-200 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-semibold text-gray-800 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 mr-2 text-blue-600\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M2 6a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 749,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 748,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"AI Avatar\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 747,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 754,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Ready\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 755,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 746,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                ref: mediaElementRef,\n                                                className: \"w-full h-96 bg-gray-900 rounded-xl object-cover shadow-lg\",\n                                                autoPlay: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-16 h-16 mx-auto mb-4 opacity-50\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                    lineNumber: 768,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 767,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-medium\",\n                                                                children: \"Avatar will appear here\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 770,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm opacity-75\",\n                                                                children: \"Click Start to begin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 771,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 766,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 760,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 759,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                    lineNumber: 745,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 743,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                        lineNumber: 626,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                    lineNumber: 625,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n            lineNumber: 590,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n        lineNumber: 589,\n        columnNumber: 5\n    }, undefined);\n};\n_s(QnAWithHeyGenModal, \"sQLL3/36pfSxc1T2aOA1CeM+JLY=\");\n_c = QnAWithHeyGenModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QnAWithHeyGenModal);\nvar _c;\n$RefreshReg$(_c, \"QnAWithHeyGenModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/QnAWithHeyGenModal.tsx\n"));

/***/ })

});