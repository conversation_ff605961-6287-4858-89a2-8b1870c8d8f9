'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Room, RoomEvent, Track, VideoPresets } from 'livekit-client';

interface QnAWithHeyGenModalProps {
  isOpen: boolean;
  onClose: () => void;
  courseId: string;
}

const QnAWithHeyGenModal: React.FC<QnAWithHeyGenModalProps> = ({ isOpen, onClose, courseId }) => {
  // Configuration
  const API_CONFIG = {
    apiKey: "NDM2Y2M0NDI0YTMzNGM1OWFkZThhZTRmMjJhZmEzNzYtMTc0NzA1OTcyOA==",
    serverUrl: "https://api.heygen.com",
  };

  // State variables
  const [sessionInfo, setSessionInfo] = useState<any>(null);
  const [sessionToken, setSessionToken] = useState<string | null>(null);
  const [room, setRoom] = useState<any>(null);
  const [mediaStream, setMediaStream] = useState<MediaStream | null>(null);
  const [webSocket, setWebSocket] = useState<WebSocket | null>(null);
  // const [avatarID, setAvatarID] = useState('Wayne_20240711');
  const [avatarID, setAvatarID] = useState('Pedro_Chair_Sitting_public');

  
  const [voiceID, setVoiceID] = useState('');
  const [taskInput, setTaskInput] = useState('');
  const [statusMessages, setStatusMessages] = useState<Array<{message: string, type: string, timestamp: string}>>([]);
  const [isStarted, setIsStarted] = useState(false);
  const [isAutoStarting, setIsAutoStarting] = useState(false);

  // Voice input states
  const [isRecording, setIsRecording] = useState(false);
  const [recognition, setRecognition] = useState<any>(null);
  const [isVoiceSupported, setIsVoiceSupported] = useState(false);

  const mediaElementRef = useRef<HTMLVideoElement>(null);
  const statusElementRef = useRef<HTMLDivElement>(null);

  // Helper function to update status with enhanced styling
  const updateStatus = (message: string, type: string = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    const newMessage = { message, type, timestamp };
    setStatusMessages(prev => [...prev, newMessage]);
  };

  // Auto-scroll status to bottom
  useEffect(() => {
    if (statusElementRef.current) {
      statusElementRef.current.scrollTop = statusElementRef.current.scrollHeight;
    }
  }, [statusMessages]);

  // Initialize status messages and auto-start avatar
  useEffect(() => {
    if (isOpen) {
      setStatusMessages([
        { message: "Welcome to AI Avatar Assistant!", type: "system", timestamp: new Date().toLocaleTimeString() },
        { message: "Auto-starting avatar session...", type: "info", timestamp: new Date().toLocaleTimeString() },
        { message: "Please wait while we initialize your AI assistant", type: "info", timestamp: new Date().toLocaleTimeString() }
      ]);

      // Set auto-starting state
      setIsAutoStarting(true);

      // Auto-start the avatar session after a brief delay to ensure component is ready
      const autoStartTimer = setTimeout(async () => {
        try {
          await handleStart();
          setIsAutoStarting(false);
        } catch (error: any) {
          console.error("Auto-start failed:", error);
          setIsAutoStarting(false);
        }
      }, 1000); // 1 second delay

      // Cleanup timer if component unmounts or modal closes
      return () => {
        clearTimeout(autoStartTimer);
        setIsAutoStarting(false);
      };
    } else {
      // Reset states when modal closes
      setIsAutoStarting(false);
    }
  }, [isOpen]);

  // Initialize voice recognition
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Check for Web Speech API support
      const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;

      if (SpeechRecognition) {
        setIsVoiceSupported(true);
        const recognitionInstance = new SpeechRecognition();

        // Configure recognition
        recognitionInstance.continuous = false;
        recognitionInstance.interimResults = false;
        recognitionInstance.lang = 'en-US';

        // Handle results
        recognitionInstance.onresult = (event: any) => {
          const transcript = event.results[0][0].transcript;
          setTaskInput(transcript);
          updateStatus(`🎤 Voice input: "${transcript}"`, "success");
        };

        // Handle errors
        recognitionInstance.onerror = (event: any) => {
          updateStatus(`Voice recognition error: ${event.error}`, "error");
          setIsRecording(false);
        };

        // Handle end
        recognitionInstance.onend = () => {
          setIsRecording(false);
        };

        setRecognition(recognitionInstance);
      } else {
        setIsVoiceSupported(false);
        updateStatus("Voice input not supported in this browser", "warning");
      }
    }
  }, []);

  // Get session token
  const getSessionToken = async () => {
    try {
      const response = await fetch(
        `${API_CONFIG.serverUrl}/v1/streaming.create_token`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-Api-Key": API_CONFIG.apiKey,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (!data.data || !data.data.token) {
        throw new Error('Invalid token data received from server');
      }

      setSessionToken(data.data.token);
      updateStatus("Session token obtained successfully", "success");
      return data.data.token;
    } catch (error: any) {
      updateStatus(`Failed to get session token: ${error.message}`, "error");
      throw error;
    }
  };

  // Connect WebSocket
  const connectWebSocket = async (sessionId: string, token: string) => {
    try {
      if (!token) {
        throw new Error('No session token available for WebSocket connection');
      }

      const params = new URLSearchParams({
        session_id: sessionId,
        session_token: token,
        silence_response: 'false',
        opening_text: "Hello, how can I help you?",
        stt_language: "en",
      });
      const wsUrl = `wss://${new URL(API_CONFIG.serverUrl).hostname}/v1/ws/streaming.chat?${params}`;
      const ws = new WebSocket(wsUrl);
      setWebSocket(ws);

      ws.addEventListener("message", (event) => {
        const eventData = JSON.parse(event.data);
        console.log("Raw WebSocket event:", eventData);
      });

      ws.addEventListener("error", (error) => {
        updateStatus("WebSocket connection error", "error");
        console.error("WebSocket error:", error);
      });

      ws.addEventListener("open", () => {
        updateStatus("WebSocket connected successfully", "success");
      });
    } catch (error: any) {
      updateStatus(`WebSocket connection failed: ${error.message}`, "error");
      throw error;
    }
  };

  // Create new session
  const createNewSession = async () => {
    try {
      // Always get a fresh token to avoid state timing issues
      const token = await getSessionToken();

      updateStatus(`Debug: Creating session with token: ${token ? 'Token exists' : 'No token'}`, "info");

      const response = await fetch(
        `${API_CONFIG.serverUrl}/v1/streaming.new`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            quality: "high",
            avatar_name: avatarID,
            voice: {
              voice_id: voiceID,
              rate: 1.0,
            },
            version: "v2",
            video_encoding: "H264",
          }),
        }
      );

      updateStatus(`Debug: Create session response status: ${response.status}`, "info");

      if (!response.ok) {
        const errorText = await response.text();
        updateStatus(`Debug: Create session error: ${errorText}`, "error");
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const data = await response.json();

      if (!data.data || !data.data.session_id) {
        throw new Error('Invalid session data received from server');
      }

      updateStatus(`Debug: Session data keys: ${Object.keys(data.data).join(', ')}`, "info");

      // Set session info and return it for immediate use
      setSessionInfo(data.data);

      // Create LiveKit Room
      const livekitRoom = new Room({
        adaptiveStream: true,
        dynacast: true,
        videoCaptureDefaults: {
          resolution: VideoPresets.h720.resolution,
        },
      });
      setRoom(livekitRoom);

      // Handle room events
      livekitRoom.on(RoomEvent.DataReceived, (message) => {
        const data = new TextDecoder().decode(message);
        console.log("Room message:", JSON.parse(data));
      });

      // Handle media streams
      const newMediaStream = new MediaStream();
      setMediaStream(newMediaStream);

      livekitRoom.on(RoomEvent.TrackSubscribed, (track) => {
        if (track.kind === "video" || track.kind === "audio") {
          newMediaStream.addTrack(track.mediaStreamTrack);
          if (
            newMediaStream.getVideoTracks().length > 0 &&
            newMediaStream.getAudioTracks().length > 0
          ) {
            if (mediaElementRef.current) {
              mediaElementRef.current.srcObject = newMediaStream;
              updateStatus("Media stream ready - Avatar is live!", "success");
            }
          }
        }
      });

      // Handle media stream removal
      livekitRoom.on(RoomEvent.TrackUnsubscribed, (track) => {
        const mediaTrack = track.mediaStreamTrack;
        if (mediaTrack) {
          newMediaStream.removeTrack(mediaTrack);
        }
      });

      // Handle room connection state changes
      livekitRoom.on(RoomEvent.Disconnected, (reason) => {
        updateStatus(`Room disconnected: ${reason}`, "warning");
      });

      await livekitRoom.prepareConnection(data.data.url, data.data.access_token);
      updateStatus("Connection prepared successfully", "success");
      await connectWebSocket(data.data.session_id, token);
      updateStatus("Session created successfully", "success");

      // Return both session data, token, and room for immediate use
      return { sessionData: data.data, token, room: livekitRoom };
    } catch (error: any) {
      updateStatus(`Failed to create session: ${error.message}`, "error");
      throw error;
    }
  };

  // Start streaming session
  const startStreamingSession = async (sessionData: any, token: string, livekitRoom: any) => {
    if (!sessionData || !sessionData.session_id) {
      throw new Error('No session info available');
    }

    try {
      updateStatus(`Debug: Using token: ${token ? 'Token exists' : 'No token'}`, "info");
      updateStatus(`Debug: Session ID: ${sessionData.session_id}`, "info");

      const startResponse = await fetch(
        `${API_CONFIG.serverUrl}/v1/streaming.start`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            session_id: sessionData.session_id,
          }),
        }
      );

      updateStatus(`Debug: Start response status: ${startResponse.status}`, "info");

      if (!startResponse.ok) {
        const errorText = await startResponse.text();
        updateStatus(`Debug: Error response: ${errorText}`, "error");
        throw new Error(`HTTP error! status: ${startResponse.status} - ${errorText}`);
      }

      // Connect to LiveKit room
      updateStatus(`Debug: Available session properties: ${Object.keys(sessionData).join(', ')}`, "info");
      updateStatus(`Debug: Room exists: ${!!livekitRoom}`, "info");
      updateStatus(`Debug: URL exists: ${!!sessionData.url}`, "info");
      updateStatus(`Debug: Access token exists: ${!!sessionData.access_token}`, "info");

      if (livekitRoom && sessionData.url && sessionData.access_token) {
        await livekitRoom.connect(sessionData.url, sessionData.access_token);
        updateStatus("Connected to room successfully", "success");
      } else {
        updateStatus("Warning: Room or connection details missing", "warning");
        updateStatus(`Debug: Missing - Room: ${!livekitRoom}, URL: ${!sessionData.url}, Token: ${!sessionData.access_token}`, "error");
      }

      setIsStarted(true);
      updateStatus("Streaming started successfully", "success");
    } catch (error: any) {
      updateStatus(`Failed to start streaming: ${error.message}`, "error");
      throw error;
    }
  };

  // Send text to avatar
  const sendText = async (text: string, taskType: string = "talk") => {
    if (!sessionInfo) {
      updateStatus("No active session - please start the avatar first", "warning");
      return;
    }

    const token = sessionToken;
    if (!token) {
      updateStatus("No session token available", "error");
      return;
    }

    try {
      updateStatus(`Sending message to avatar...`, "info");
      const response = await fetch(
        `${API_CONFIG.serverUrl}/v1/streaming.task`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            session_id: sessionInfo.session_id,
            text: text,
            task_type: taskType,
          }),
        }
      );
      const data = await response.json();
      if (!response.ok || data.error) {
        updateStatus(`HeyGen API error: ${data.error || response.statusText}`, "error");
      } else {
        updateStatus(`✓ Message sent successfully (${taskType})`, "success");
        updateStatus(`Avatar will speak: "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`, "info");
      }
    } catch (err: any) {
      updateStatus("HeyGen API call failed: " + err.message, "error");
    }
  };

  // Close session
  const closeSession = async () => {
    if (!sessionInfo) {
      updateStatus("No active session", "warning");
      return;
    }

    try {
      const token = sessionToken;
      if (token) {
        const response = await fetch(
          `${API_CONFIG.serverUrl}/v1/streaming.stop`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({
              session_id: sessionInfo.session_id,
            }),
          }
        );
      }

      // Close WebSocket
      if (webSocket) {
        webSocket.close();
      }

      // Disconnect from LiveKit room
      if (room) {
        room.disconnect();
      }

      // Reset states
      if (mediaElementRef.current) {
        mediaElementRef.current.srcObject = null;
      }
      setSessionInfo(null);
      setRoom(null);
      setMediaStream(null);
      setSessionToken(null);
      setIsStarted(false);
      updateStatus("Session closed successfully", "warning");
    } catch (error: any) {
      updateStatus(`Error closing session: ${error.message}`, "error");
      // Still reset states even if API call fails
      setSessionInfo(null);
      setRoom(null);
      setMediaStream(null);
      setSessionToken(null);
      setIsStarted(false);
    }
  };

  // Azure Search and OpenAI integration
  const callAzureOpenAI = async (prompt: string) => {
    try {
      const response = await fetch("http://localhost:5001/api/openai", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ prompt })
      });
      const data = await response.json();
      if (!response.ok || !data.choices) {
        const errorMsg = data.error ? (typeof data.error === 'object' ? JSON.stringify(data.error) : data.error) : response.statusText;
        updateStatus("Azure OpenAI error: " + errorMsg, "error");
        return null;
      }
      return data.choices?.[0]?.message?.content;
    } catch (err: any) {
      updateStatus("Azure OpenAI call failed: " + err.message, "error");
      return null;
    }
  };

  const searchAzure = async (query: string) => {
    try {
      const response = await fetch("http://localhost:5001/api/search", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ query, courseId })
      });
      const data = await response.json();
      if (!response.ok || !data.value) {
        const errorMsg = data.error ? (typeof data.error === 'object' ? JSON.stringify(data.error) : data.error) : response.statusText;
        updateStatus("Azure Search error: " + errorMsg, "error");
        return null;
      }
      updateStatus(`Found ${data.value.length} relevant documents`, "success");
      return (data.value || []).map((r: any) => r.content).join('\n\n');
    } catch (err: any) {
      updateStatus("Azure Search call failed: " + err.message, "error");
      return null;
    }
  };

  // Handle Ask AI button
  const handleAskAI = async () => {
    const text = taskInput.trim();
    if (!text) {
      updateStatus("Please enter a question first", "warning");
      return;
    }

    if (!sessionInfo) {
      updateStatus("Please start the avatar session first", "warning");
      return;
    }

    try {
      updateStatus(`🔍 Searching for: "${text}"`, "info");
      const searchResults = await searchAzure(text);
      if (!searchResults) {
        updateStatus("No relevant documents found for your question", "warning");
        return;
      }

      updateStatus("🤖 Processing with AI...", "info");
      const llmAnswer = await callAzureOpenAI(`Based on the following context, answer the question: "${text}"\n\nContext: ${searchResults}`);

      if (llmAnswer) {
        updateStatus(`💡 AI Response: ${llmAnswer.substring(0, 100)}${llmAnswer.length > 100 ? '...' : ''}`, "success");

        // Clean the response for avatar speech
        let cleaned = llmAnswer.replace(/[#*_`>-]+/g, '').replace(/\n{2,}/g, ' ').replace(/\s{2,}/g, ' ').trim();

        updateStatus(`🎤 Avatar speaking response...`, "info");
        await sendText(cleaned, "repeat");
        setTaskInput("");
      } else {
        updateStatus("Failed to generate AI response", "error");
      }
    } catch (error: any) {
      updateStatus(`Error processing question: ${error.message}`, "error");
    }
  };

  // Handle Voice Input button
  const handleVoiceInput = () => {
    if (!isVoiceSupported) {
      updateStatus("Voice input not supported in this browser", "warning");
      return;
    }

    if (!recognition) {
      updateStatus("Voice recognition not initialized", "error");
      return;
    }

    if (isRecording) {
      // Stop recording
      recognition.stop();
      setIsRecording(false);
      updateStatus("Voice recording stopped", "info");
    } else {
      // Start recording
      try {
        recognition.start();
        setIsRecording(true);
        updateStatus("🎤 Listening... Speak your question", "info");
      } catch (error: any) {
        updateStatus(`Failed to start voice recording: ${error.message}`, "error");
        setIsRecording(false);
      }
    }
  };



  // Handle Start button
  const handleStart = async () => {
    try {
      const result = await createNewSession();
      await startStreamingSession(result.sessionData, result.token, result.room);
    } catch (error: any) {
      updateStatus(`Error starting session: ${error.message}`, "error");
    }
  };

  // Handle Enter key in textarea
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleAskAI();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <div>
                <h1 className="text-xl font-bold">AI Avatar Assistant</h1>
                <p className="text-white text-opacity-70 text-sm">Powered by EXL</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${isStarted ? 'bg-green-400 animate-pulse' : 'bg-gray-400'}`}></div>
                <span className="text-white text-opacity-80 text-sm font-medium">
                  {isStarted ? 'Live' : 'Offline'}
                </span>
              </div>
              <button
                onClick={onClose}
                className="text-white hover:text-gray-200 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="p-6 max-h-[calc(90vh-120px)] overflow-y-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            
            {/* Left Panel - Controls */}
            <div className="lg:col-span-1">
              <div className="bg-gray-50 rounded-2xl p-6 border border-gray-200">
                <h2 className="text-lg font-semibold text-gray-800 mb-6 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"/>
                  </svg>
                  Avatar Configuration
                </h2>
                
                {/* Avatar Settings */}
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Avatar ID</label>
                    <input 
                      type="text" 
                      placeholder="Enter Avatar ID" 
                      value={avatarID}
                      onChange={(e) => setAvatarID(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200" 
                    />
                  </div>
                  
                  {/* <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Voice ID</label>
                    <input 
                      type="text" 
                      placeholder="Enter Voice ID" 
                      value={voiceID}
                      onChange={(e) => setVoiceID(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200" 
                    />
                  </div> */}
                  
                  {/* Control Buttons */}
                  <div className="grid grid-cols-2 gap-3 mt-6">
                    <button
                      onClick={handleStart}
                      disabled={isStarted || isAutoStarting}
                      className="px-4 py-3 bg-green-500 hover:bg-green-600 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none shadow-lg"
                    >
                      {isAutoStarting ? (
                        <>
                          <svg className="w-4 h-4 inline mr-2 animate-spin" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd"/>
                          </svg>
                          Auto-Starting...
                        </>
                      ) : (
                        <>
                          <svg className="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd"/>
                          </svg>
                          {isStarted ? 'Connected' : 'Start'}
                        </>
                      )}
                    </button>
                    <button
                      onClick={closeSession}
                      className="px-4 py-3 bg-red-500 hover:bg-red-600 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg"
                    >
                      <svg className="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"/>
                      </svg>
                      Stop
                    </button>
                  </div>
                </div>
              </div>

              {/* Chat Input Panel */}
              <div className="bg-gray-50 rounded-2xl p-6 border border-gray-200 mt-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-cyan-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd"/>
                  </svg>
                  Ask Your Question
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <textarea
                      placeholder="Type your question here..."
                      value={taskInput}
                      onChange={(e) => setTaskInput(e.target.value)}
                      onKeyDown={handleKeyDown}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-200 resize-none h-24"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <button
                      onClick={handleAskAI}
                      className="px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg"
                    >
                      <svg className="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                      Ask AI
                    </button>
                    <button
                      onClick={handleVoiceInput}
                      disabled={!isVoiceSupported}
                      className={`px-4 py-3 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg ${
                        isRecording
                          ? 'bg-red-500 hover:bg-red-600 animate-pulse'
                          : isVoiceSupported
                            ? 'bg-purple-500 hover:bg-purple-600'
                            : 'bg-gray-400 cursor-not-allowed'
                      }`}
                    >
                      <svg className="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                        {isRecording ? (
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a2 2 0 114 0v4a2 2 0 11-4 0V7z" clipRule="evenodd"/>
                        ) : (
                          <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd"/>
                        )}
                      </svg>
                      {isRecording ? 'Stop Recording' : 'Voice Input'}
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Panel - Video & Status */}
            <div className="lg:col-span-2">
              {/* Video Container */}
              <div className="bg-gray-50 rounded-2xl p-6 border border-gray-200 mb-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold text-gray-800 flex items-center">
                    <svg className="w-5 h-5 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"/>
                    </svg>
                    AI Avatar
                  </h2>
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${
                      isAutoStarting
                        ? 'bg-yellow-500 animate-pulse'
                        : isStarted
                          ? 'bg-green-500 animate-pulse'
                          : 'bg-gray-400'
                    }`}></div>
                    <span className="text-sm text-gray-600">
                      {isAutoStarting ? 'Starting...' : isStarted ? 'Connected' : 'Ready'}
                    </span>
                  </div>
                </div>
                
                <div className="relative">
                  <video 
                    ref={mediaElementRef}
                    className="w-full h-96 bg-gray-900 rounded-xl object-cover shadow-lg" 
                    autoPlay
                  >
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center text-white">
                        <svg className="w-16 h-16 mx-auto mb-4 opacity-50" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd"/>
                        </svg>
                        <p className="text-lg font-medium">Avatar will appear here</p>
                        <p className="text-sm opacity-75">Click Start to begin</p>
                      </div>
                    </div>
                  </video>
                </div>
              </div>

              {/* Status Panel */}
              {/* <div className="bg-gray-50 rounded-2xl p-6 border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"/>
                  </svg>
                  System Status
                </h3>
                <div 
                  ref={statusElementRef}
                  className="bg-gray-900 text-green-400 p-4 rounded-xl h-48 overflow-y-auto font-mono text-sm border border-gray-700"
                >
                  <div className="text-cyan-400">[System] AI Avatar Assistant initialized</div>
                  <div className="text-gray-400">[Info] Ready to start conversation...</div>
                  {statusMessages.map((msg, index) => (
                    <div key={index} className={`${
                      msg.type === 'success' ? 'text-green-400' :
                      msg.type === 'error' ? 'text-red-400' :
                      msg.type === 'warning' ? 'text-yellow-400' :
                      msg.type === 'system' ? 'text-cyan-400' : 'text-blue-400'
                    }`}>
                      [{msg.type === 'system' ? 'System' : msg.type === 'error' ? 'Error' : 'Info'}] [{msg.timestamp}] {msg.message}
                    </div>
                  ))}
                </div>
              </div> */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QnAWithHeyGenModal;
