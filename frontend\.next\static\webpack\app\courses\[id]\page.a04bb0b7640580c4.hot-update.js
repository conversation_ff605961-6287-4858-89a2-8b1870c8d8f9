"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/courses/[id]/page",{

/***/ "(app-pages-browser)/./src/components/QnAWithHeyGenModal.tsx":
/*!***********************************************!*\
  !*** ./src/components/QnAWithHeyGenModal.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var livekit_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! livekit-client */ \"(app-pages-browser)/./node_modules/livekit-client/dist/livekit-client.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst QnAWithHeyGenModal = (param)=>{\n    let { isOpen, onClose, courseId } = param;\n    _s();\n    // Configuration\n    const API_CONFIG = {\n        apiKey: \"NDM2Y2M0NDI0YTMzNGM1OWFkZThhZTRmMjJhZmEzNzYtMTc0NzA1OTcyOA==\",\n        serverUrl: \"https://api.heygen.com\"\n    };\n    // State variables\n    const [sessionInfo, setSessionInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sessionToken, setSessionToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [room, setRoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mediaStream, setMediaStream] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [webSocket, setWebSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // const [avatarID, setAvatarID] = useState('Wayne_20240711');\n    const [avatarID, setAvatarID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Pedro_Chair_Sitting_public');\n    const [voiceID, setVoiceID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [taskInput, setTaskInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusMessages, setStatusMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isStarted, setIsStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAutoStarting, setIsAutoStarting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Voice input states\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recognition, setRecognition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isVoiceSupported, setIsVoiceSupported] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const mediaElementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const statusElementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Helper function to update status with enhanced styling\n    const updateStatus = function(message) {\n        let type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'info';\n        const timestamp = new Date().toLocaleTimeString();\n        const newMessage = {\n            message,\n            type,\n            timestamp\n        };\n        setStatusMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n    };\n    // Auto-scroll status to bottom\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QnAWithHeyGenModal.useEffect\": ()=>{\n            if (statusElementRef.current) {\n                statusElementRef.current.scrollTop = statusElementRef.current.scrollHeight;\n            }\n        }\n    }[\"QnAWithHeyGenModal.useEffect\"], [\n        statusMessages\n    ]);\n    // Initialize status messages and auto-start avatar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QnAWithHeyGenModal.useEffect\": ()=>{\n            if (isOpen) {\n                setStatusMessages([\n                    {\n                        message: \"Welcome to AI Avatar Assistant!\",\n                        type: \"system\",\n                        timestamp: new Date().toLocaleTimeString()\n                    },\n                    {\n                        message: \"Auto-starting avatar session...\",\n                        type: \"info\",\n                        timestamp: new Date().toLocaleTimeString()\n                    },\n                    {\n                        message: \"Please wait while we initialize your AI assistant\",\n                        type: \"info\",\n                        timestamp: new Date().toLocaleTimeString()\n                    }\n                ]);\n                // Set auto-starting state\n                setIsAutoStarting(true);\n                // Auto-start the avatar session after a brief delay to ensure component is ready\n                const autoStartTimer = setTimeout({\n                    \"QnAWithHeyGenModal.useEffect.autoStartTimer\": async ()=>{\n                        try {\n                            await handleStart();\n                            setIsAutoStarting(false);\n                        } catch (error) {\n                            console.error(\"Auto-start failed:\", error);\n                            setIsAutoStarting(false);\n                        }\n                    }\n                }[\"QnAWithHeyGenModal.useEffect.autoStartTimer\"], 1000); // 1 second delay\n                // Cleanup timer if component unmounts or modal closes\n                return ({\n                    \"QnAWithHeyGenModal.useEffect\": ()=>{\n                        clearTimeout(autoStartTimer);\n                        setIsAutoStarting(false);\n                    }\n                })[\"QnAWithHeyGenModal.useEffect\"];\n            } else {\n                // Reset states when modal closes\n                setIsAutoStarting(false);\n            }\n        }\n    }[\"QnAWithHeyGenModal.useEffect\"], [\n        isOpen\n    ]);\n    // Initialize voice recognition\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QnAWithHeyGenModal.useEffect\": ()=>{\n            if (true) {\n                // Check for Web Speech API support\n                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n                if (SpeechRecognition) {\n                    setIsVoiceSupported(true);\n                    const recognitionInstance = new SpeechRecognition();\n                    // Configure recognition\n                    recognitionInstance.continuous = false;\n                    recognitionInstance.interimResults = false;\n                    recognitionInstance.lang = 'en-US';\n                    // Handle results\n                    recognitionInstance.onresult = ({\n                        \"QnAWithHeyGenModal.useEffect\": (event)=>{\n                            const transcript = event.results[0][0].transcript;\n                            setTaskInput(transcript);\n                            updateStatus('\\uD83C\\uDFA4 Voice input: \"'.concat(transcript, '\"'), \"success\");\n                        }\n                    })[\"QnAWithHeyGenModal.useEffect\"];\n                    // Handle errors\n                    recognitionInstance.onerror = ({\n                        \"QnAWithHeyGenModal.useEffect\": (event)=>{\n                            updateStatus(\"Voice recognition error: \".concat(event.error), \"error\");\n                            setIsRecording(false);\n                        }\n                    })[\"QnAWithHeyGenModal.useEffect\"];\n                    // Handle end\n                    recognitionInstance.onend = ({\n                        \"QnAWithHeyGenModal.useEffect\": ()=>{\n                            setIsRecording(false);\n                        }\n                    })[\"QnAWithHeyGenModal.useEffect\"];\n                    setRecognition(recognitionInstance);\n                } else {\n                    setIsVoiceSupported(false);\n                    updateStatus(\"Voice input not supported in this browser\", \"warning\");\n                }\n            }\n        }\n    }[\"QnAWithHeyGenModal.useEffect\"], []);\n    // Get session token\n    const getSessionToken = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.create_token\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"X-Api-Key\": API_CONFIG.apiKey\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            if (!data.data || !data.data.token) {\n                throw new Error('Invalid token data received from server');\n            }\n            setSessionToken(data.data.token);\n            updateStatus(\"Session token obtained successfully\", \"success\");\n            return data.data.token;\n        } catch (error) {\n            updateStatus(\"Failed to get session token: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Connect WebSocket\n    const connectWebSocket = async (sessionId, token)=>{\n        try {\n            if (!token) {\n                throw new Error('No session token available for WebSocket connection');\n            }\n            const params = new URLSearchParams({\n                session_id: sessionId,\n                session_token: token,\n                silence_response: 'false',\n                opening_text: \"Hello, how can I help you?\",\n                stt_language: \"en\"\n            });\n            const wsUrl = \"wss://\".concat(new URL(API_CONFIG.serverUrl).hostname, \"/v1/ws/streaming.chat?\").concat(params);\n            const ws = new WebSocket(wsUrl);\n            setWebSocket(ws);\n            ws.addEventListener(\"message\", (event)=>{\n                const eventData = JSON.parse(event.data);\n                console.log(\"Raw WebSocket event:\", eventData);\n            });\n            ws.addEventListener(\"error\", (error)=>{\n                updateStatus(\"WebSocket connection error\", \"error\");\n                console.error(\"WebSocket error:\", error);\n            });\n            ws.addEventListener(\"open\", ()=>{\n                updateStatus(\"WebSocket connected successfully\", \"success\");\n            });\n        } catch (error) {\n            updateStatus(\"WebSocket connection failed: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Create new session\n    const createNewSession = async ()=>{\n        try {\n            // Always get a fresh token to avoid state timing issues\n            const token = await getSessionToken();\n            updateStatus(\"Debug: Creating session with token: \".concat(token ? 'Token exists' : 'No token'), \"info\");\n            const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.new\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    quality: \"high\",\n                    avatar_name: avatarID,\n                    voice: {\n                        voice_id: voiceID,\n                        rate: 1.0\n                    },\n                    version: \"v2\",\n                    video_encoding: \"H264\"\n                })\n            });\n            updateStatus(\"Debug: Create session response status: \".concat(response.status), \"info\");\n            if (!response.ok) {\n                const errorText = await response.text();\n                updateStatus(\"Debug: Create session error: \".concat(errorText), \"error\");\n                throw new Error(\"HTTP error! status: \".concat(response.status, \" - \").concat(errorText));\n            }\n            const data = await response.json();\n            if (!data.data || !data.data.session_id) {\n                throw new Error('Invalid session data received from server');\n            }\n            updateStatus(\"Debug: Session data keys: \".concat(Object.keys(data.data).join(', ')), \"info\");\n            // Set session info and return it for immediate use\n            setSessionInfo(data.data);\n            // Create LiveKit Room\n            const livekitRoom = new livekit_client__WEBPACK_IMPORTED_MODULE_2__.Room({\n                adaptiveStream: true,\n                dynacast: true,\n                videoCaptureDefaults: {\n                    resolution: livekit_client__WEBPACK_IMPORTED_MODULE_2__.VideoPresets.h720.resolution\n                }\n            });\n            setRoom(livekitRoom);\n            // Handle room events\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.DataReceived, (message)=>{\n                const data = new TextDecoder().decode(message);\n                console.log(\"Room message:\", JSON.parse(data));\n            });\n            // Handle media streams\n            const newMediaStream = new MediaStream();\n            setMediaStream(newMediaStream);\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.TrackSubscribed, (track)=>{\n                if (track.kind === \"video\" || track.kind === \"audio\") {\n                    newMediaStream.addTrack(track.mediaStreamTrack);\n                    if (newMediaStream.getVideoTracks().length > 0 && newMediaStream.getAudioTracks().length > 0) {\n                        if (mediaElementRef.current) {\n                            mediaElementRef.current.srcObject = newMediaStream;\n                            updateStatus(\"Media stream ready - Avatar is live!\", \"success\");\n                        }\n                    }\n                }\n            });\n            // Handle media stream removal\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.TrackUnsubscribed, (track)=>{\n                const mediaTrack = track.mediaStreamTrack;\n                if (mediaTrack) {\n                    newMediaStream.removeTrack(mediaTrack);\n                }\n            });\n            // Handle room connection state changes\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.Disconnected, (reason)=>{\n                updateStatus(\"Room disconnected: \".concat(reason), \"warning\");\n            });\n            await livekitRoom.prepareConnection(data.data.url, data.data.access_token);\n            updateStatus(\"Connection prepared successfully\", \"success\");\n            await connectWebSocket(data.data.session_id, token);\n            updateStatus(\"Session created successfully\", \"success\");\n            // Return both session data, token, and room for immediate use\n            return {\n                sessionData: data.data,\n                token,\n                room: livekitRoom\n            };\n        } catch (error) {\n            updateStatus(\"Failed to create session: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Start streaming session\n    const startStreamingSession = async (sessionData, token, livekitRoom)=>{\n        if (!sessionData || !sessionData.session_id) {\n            throw new Error('No session info available');\n        }\n        try {\n            updateStatus(\"Debug: Using token: \".concat(token ? 'Token exists' : 'No token'), \"info\");\n            updateStatus(\"Debug: Session ID: \".concat(sessionData.session_id), \"info\");\n            const startResponse = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.start\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    session_id: sessionData.session_id\n                })\n            });\n            updateStatus(\"Debug: Start response status: \".concat(startResponse.status), \"info\");\n            if (!startResponse.ok) {\n                const errorText = await startResponse.text();\n                updateStatus(\"Debug: Error response: \".concat(errorText), \"error\");\n                throw new Error(\"HTTP error! status: \".concat(startResponse.status, \" - \").concat(errorText));\n            }\n            // Connect to LiveKit room\n            updateStatus(\"Debug: Available session properties: \".concat(Object.keys(sessionData).join(', ')), \"info\");\n            updateStatus(\"Debug: Room exists: \".concat(!!livekitRoom), \"info\");\n            updateStatus(\"Debug: URL exists: \".concat(!!sessionData.url), \"info\");\n            updateStatus(\"Debug: Access token exists: \".concat(!!sessionData.access_token), \"info\");\n            if (livekitRoom && sessionData.url && sessionData.access_token) {\n                await livekitRoom.connect(sessionData.url, sessionData.access_token);\n                updateStatus(\"Connected to room successfully\", \"success\");\n            } else {\n                updateStatus(\"Warning: Room or connection details missing\", \"warning\");\n                updateStatus(\"Debug: Missing - Room: \".concat(!livekitRoom, \", URL: \").concat(!sessionData.url, \", Token: \").concat(!sessionData.access_token), \"error\");\n            }\n            setIsStarted(true);\n            updateStatus(\"Streaming started successfully\", \"success\");\n        } catch (error) {\n            updateStatus(\"Failed to start streaming: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Send text to avatar\n    const sendText = async function(text) {\n        let taskType = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"talk\";\n        if (!sessionInfo) {\n            updateStatus(\"No active session - please start the avatar first\", \"warning\");\n            return;\n        }\n        const token = sessionToken;\n        if (!token) {\n            updateStatus(\"No session token available\", \"error\");\n            return;\n        }\n        try {\n            updateStatus(\"Sending message to avatar...\", \"info\");\n            const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.task\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    session_id: sessionInfo.session_id,\n                    text: text,\n                    task_type: taskType\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || data.error) {\n                updateStatus(\"HeyGen API error: \".concat(data.error || response.statusText), \"error\");\n            } else {\n                updateStatus(\"✓ Message sent successfully (\".concat(taskType, \")\"), \"success\");\n                updateStatus('Avatar will speak: \"'.concat(text.substring(0, 50)).concat(text.length > 50 ? '...' : '', '\"'), \"info\");\n            }\n        } catch (err) {\n            updateStatus(\"HeyGen API call failed: \" + err.message, \"error\");\n        }\n    };\n    // Close session\n    const closeSession = async ()=>{\n        if (!sessionInfo) {\n            updateStatus(\"No active session\", \"warning\");\n            return;\n        }\n        try {\n            const token = sessionToken;\n            if (token) {\n                const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.stop\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        Authorization: \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        session_id: sessionInfo.session_id\n                    })\n                });\n            }\n            // Close WebSocket\n            if (webSocket) {\n                webSocket.close();\n            }\n            // Disconnect from LiveKit room\n            if (room) {\n                room.disconnect();\n            }\n            // Reset states\n            if (mediaElementRef.current) {\n                mediaElementRef.current.srcObject = null;\n            }\n            setSessionInfo(null);\n            setRoom(null);\n            setMediaStream(null);\n            setSessionToken(null);\n            setIsStarted(false);\n            updateStatus(\"Session closed successfully\", \"warning\");\n        } catch (error) {\n            updateStatus(\"Error closing session: \".concat(error.message), \"error\");\n            // Still reset states even if API call fails\n            setSessionInfo(null);\n            setRoom(null);\n            setMediaStream(null);\n            setSessionToken(null);\n            setIsStarted(false);\n        }\n    };\n    // Azure Search and OpenAI integration\n    const callAzureOpenAI = async (prompt)=>{\n        try {\n            var _data_choices__message, _data_choices_, _data_choices;\n            const response = await fetch(\"http://localhost:5001/api/openai\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    prompt\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || !data.choices) {\n                const errorMsg = data.error ? typeof data.error === 'object' ? JSON.stringify(data.error) : data.error : response.statusText;\n                updateStatus(\"Azure OpenAI error: \" + errorMsg, \"error\");\n                return null;\n            }\n            return (_data_choices = data.choices) === null || _data_choices === void 0 ? void 0 : (_data_choices_ = _data_choices[0]) === null || _data_choices_ === void 0 ? void 0 : (_data_choices__message = _data_choices_.message) === null || _data_choices__message === void 0 ? void 0 : _data_choices__message.content;\n        } catch (err) {\n            updateStatus(\"Azure OpenAI call failed: \" + err.message, \"error\");\n            return null;\n        }\n    };\n    const searchAzure = async (query)=>{\n        try {\n            const response = await fetch(\"http://localhost:5001/api/search\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    query,\n                    courseId\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || !data.value) {\n                const errorMsg = data.error ? typeof data.error === 'object' ? JSON.stringify(data.error) : data.error : response.statusText;\n                updateStatus(\"Azure Search error: \" + errorMsg, \"error\");\n                return null;\n            }\n            updateStatus(\"Found \".concat(data.value.length, \" relevant documents\"), \"success\");\n            return (data.value || []).map((r)=>r.content).join('\\n\\n');\n        } catch (err) {\n            updateStatus(\"Azure Search call failed: \" + err.message, \"error\");\n            return null;\n        }\n    };\n    // Handle Ask AI button\n    const handleAskAI = async ()=>{\n        const text = taskInput.trim();\n        if (!text) {\n            updateStatus(\"Please enter a question first\", \"warning\");\n            return;\n        }\n        if (!sessionInfo) {\n            updateStatus(\"Please start the avatar session first\", \"warning\");\n            return;\n        }\n        try {\n            updateStatus('\\uD83D\\uDD0D Searching for: \"'.concat(text, '\"'), \"info\");\n            const searchResults = await searchAzure(text);\n            if (!searchResults) {\n                updateStatus(\"No relevant documents found for your question\", \"warning\");\n                return;\n            }\n            updateStatus(\"🤖 Processing with AI...\", \"info\");\n            const llmAnswer = await callAzureOpenAI('Based on the following context, answer the question: \"'.concat(text, '\"\\n\\nContext: ').concat(searchResults));\n            if (llmAnswer) {\n                updateStatus(\"\\uD83D\\uDCA1 AI Response: \".concat(llmAnswer.substring(0, 100)).concat(llmAnswer.length > 100 ? '...' : ''), \"success\");\n                // Clean the response for avatar speech\n                let cleaned = llmAnswer.replace(/[#*_`>-]+/g, '').replace(/\\n{2,}/g, ' ').replace(/\\s{2,}/g, ' ').trim();\n                updateStatus(\"\\uD83C\\uDFA4 Avatar speaking response...\", \"info\");\n                await sendText(cleaned, \"repeat\");\n                setTaskInput(\"\");\n            } else {\n                updateStatus(\"Failed to generate AI response\", \"error\");\n            }\n        } catch (error) {\n            updateStatus(\"Error processing question: \".concat(error.message), \"error\");\n        }\n    };\n    // Handle Voice Input button\n    const handleVoiceInput = ()=>{\n        if (!isVoiceSupported) {\n            updateStatus(\"Voice input not supported in this browser\", \"warning\");\n            return;\n        }\n        if (!recognition) {\n            updateStatus(\"Voice recognition not initialized\", \"error\");\n            return;\n        }\n        if (isRecording) {\n            // Stop recording\n            recognition.stop();\n            setIsRecording(false);\n            updateStatus(\"Voice recording stopped\", \"info\");\n        } else {\n            // Start recording\n            try {\n                recognition.start();\n                setIsRecording(true);\n                updateStatus(\"🎤 Listening... Speak your question\", \"info\");\n            } catch (error) {\n                updateStatus(\"Failed to start voice recording: \".concat(error.message), \"error\");\n                setIsRecording(false);\n            }\n        }\n    };\n    // Handle Start button\n    const handleStart = async ()=>{\n        try {\n            const result = await createNewSession();\n            await startStreamingSession(result.sessionData, result.token, result.room);\n        } catch (error) {\n            updateStatus(\"Error starting session: \".concat(error.message), \"error\");\n        }\n    };\n    // Handle Enter key in textarea\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleAskAI();\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-white\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold\",\n                                                children: \"AI Avatar Assistant\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white text-opacity-70 text-sm\",\n                                                children: \"Powered by EXL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 610,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 604,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full \".concat(isStarted ? 'bg-green-400 animate-pulse' : 'bg-gray-400')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-opacity-80 text-sm font-medium\",\n                                                children: isStarted ? 'Live' : 'Offline'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 616,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        className: \"text-white hover:text-gray-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 615,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                        lineNumber: 603,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                    lineNumber: 602,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 max-h-[calc(90vh-120px)] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-2xl p-6 border border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800 mb-6 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2 text-blue-600\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Avatar Configuration\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Avatar ID\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 651,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: \"Enter Avatar ID\",\n                                                                value: avatarID,\n                                                                onChange: (e)=>setAvatarID(e.target.value),\n                                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 652,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-3 mt-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleStart,\n                                                                disabled: isStarted || isAutoStarting,\n                                                                className: \"px-4 py-3 bg-green-500 hover:bg-green-600 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none shadow-lg\",\n                                                                children: isAutoStarting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 inline mr-2 animate-spin\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                                lineNumber: 682,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 681,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \"Auto-Starting...\"\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 inline mr-2\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 20 20\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                fillRule: \"evenodd\",\n                                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                                                clipRule: \"evenodd\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                                lineNumber: 689,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 688,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        isStarted ? 'Connected' : 'Start'\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 674,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: closeSession,\n                                                                className: \"px-4 py-3 bg-red-500 hover:bg-red-600 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 inline mr-2\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 700,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 699,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \"Stop\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 695,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-2xl p-6 border border-gray-200 mt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-800 mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2 text-cyan-600\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 712,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Ask Your Question\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            placeholder: \"Type your question here...\",\n                                                            value: taskInput,\n                                                            onChange: (e)=>setTaskInput(e.target.value),\n                                                            onKeyDown: handleKeyDown,\n                                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-200 resize-none h-24\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 719,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleAskAI,\n                                                                className: \"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 inline mr-2\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 734,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 733,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \"Ask AI\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleVoiceInput,\n                                                                disabled: !isVoiceSupported,\n                                                                className: \"px-4 py-3 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg \".concat(isRecording ? 'bg-red-500 hover:bg-red-600 animate-pulse' : isVoiceSupported ? 'bg-purple-500 hover:bg-purple-600' : 'bg-gray-400 cursor-not-allowed'),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 inline mr-2\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a2 2 0 114 0v4a2 2 0 11-4 0V7z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 751,\n                                                                            columnNumber: 27\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 753,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 749,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    isRecording ? 'Stop Recording' : 'Voice Input'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 738,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 709,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 639,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-2xl p-6 border border-gray-200 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-semibold text-gray-800 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 mr-2 text-blue-600\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M2 6a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 770,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 769,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"AI Avatar\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 768,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full \".concat(isAutoStarting ? 'bg-yellow-500 animate-pulse' : isStarted ? 'bg-green-500 animate-pulse' : 'bg-gray-400')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 775,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: isAutoStarting ? 'Starting...' : isStarted ? 'Connected' : 'Ready'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 782,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 774,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 767,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                ref: mediaElementRef,\n                                                className: \"w-full h-96 bg-gray-900 rounded-xl object-cover shadow-lg\",\n                                                autoPlay: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-16 h-16 mx-auto mb-4 opacity-50\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                    lineNumber: 797,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 796,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-medium\",\n                                                                children: \"Avatar will appear here\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 799,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm opacity-75\",\n                                                                children: \"Click Start to begin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 800,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 795,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 794,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 789,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 788,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                    lineNumber: 766,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 764,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                        lineNumber: 636,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                    lineNumber: 635,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n            lineNumber: 600,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n        lineNumber: 599,\n        columnNumber: 5\n    }, undefined);\n};\n_s(QnAWithHeyGenModal, \"KD4zp0K+Vr4yX6MzZrH14V/PMpU=\");\n_c = QnAWithHeyGenModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QnAWithHeyGenModal);\nvar _c;\n$RefreshReg$(_c, \"QnAWithHeyGenModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/QnAWithHeyGenModal.tsx\n"));

/***/ })

});