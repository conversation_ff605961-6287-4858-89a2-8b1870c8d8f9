"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/courses/[id]/page",{

/***/ "(app-pages-browser)/./src/components/QnAWithHeyGenModal.tsx":
/*!***********************************************!*\
  !*** ./src/components/QnAWithHeyGenModal.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var livekit_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! livekit-client */ \"(app-pages-browser)/./node_modules/livekit-client/dist/livekit-client.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst QnAWithHeyGenModal = (param)=>{\n    let { isOpen, onClose, courseId } = param;\n    _s();\n    // Configuration\n    const API_CONFIG = {\n        apiKey: \"NDM2Y2M0NDI0YTMzNGM1OWFkZThhZTRmMjJhZmEzNzYtMTc0NzA1OTcyOA==\",\n        serverUrl: \"https://api.heygen.com\"\n    };\n    // State variables\n    const [sessionInfo, setSessionInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sessionToken, setSessionToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [room, setRoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mediaStream, setMediaStream] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [webSocket, setWebSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // const [avatarID, setAvatarID] = useState('Wayne_20240711');\n    const [avatarID, setAvatarID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Pedro_Chair_Sitting_public');\n    const [voiceID, setVoiceID] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [taskInput, setTaskInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusMessages, setStatusMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isStarted, setIsStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAutoStarting, setIsAutoStarting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Voice input states\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recognition, setRecognition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isVoiceSupported, setIsVoiceSupported] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const mediaElementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const statusElementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Helper function to update status with enhanced styling\n    const updateStatus = function(message) {\n        let type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'info';\n        const timestamp = new Date().toLocaleTimeString();\n        const newMessage = {\n            message,\n            type,\n            timestamp\n        };\n        setStatusMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n    };\n    // Auto-scroll status to bottom\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QnAWithHeyGenModal.useEffect\": ()=>{\n            if (statusElementRef.current) {\n                statusElementRef.current.scrollTop = statusElementRef.current.scrollHeight;\n            }\n        }\n    }[\"QnAWithHeyGenModal.useEffect\"], [\n        statusMessages\n    ]);\n    // Initialize status messages and auto-start avatar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QnAWithHeyGenModal.useEffect\": ()=>{\n            if (isOpen) {\n                setStatusMessages([\n                    {\n                        message: \"Welcome to AI Avatar Assistant!\",\n                        type: \"system\",\n                        timestamp: new Date().toLocaleTimeString()\n                    },\n                    {\n                        message: \"Auto-starting avatar session...\",\n                        type: \"info\",\n                        timestamp: new Date().toLocaleTimeString()\n                    },\n                    {\n                        message: \"Please wait while we initialize your AI assistant\",\n                        type: \"info\",\n                        timestamp: new Date().toLocaleTimeString()\n                    }\n                ]);\n                // Auto-start the avatar session after a brief delay to ensure component is ready\n                const autoStartTimer = setTimeout({\n                    \"QnAWithHeyGenModal.useEffect.autoStartTimer\": async ()=>{\n                        try {\n                            await handleStart();\n                        } catch (error) {\n                            console.error(\"Auto-start failed:\", error);\n                        }\n                    }\n                }[\"QnAWithHeyGenModal.useEffect.autoStartTimer\"], 1000); // 1 second delay\n                // Cleanup timer if component unmounts or modal closes\n                return ({\n                    \"QnAWithHeyGenModal.useEffect\": ()=>{\n                        clearTimeout(autoStartTimer);\n                    }\n                })[\"QnAWithHeyGenModal.useEffect\"];\n            }\n        }\n    }[\"QnAWithHeyGenModal.useEffect\"], [\n        isOpen\n    ]);\n    // Initialize voice recognition\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QnAWithHeyGenModal.useEffect\": ()=>{\n            if (true) {\n                // Check for Web Speech API support\n                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n                if (SpeechRecognition) {\n                    setIsVoiceSupported(true);\n                    const recognitionInstance = new SpeechRecognition();\n                    // Configure recognition\n                    recognitionInstance.continuous = false;\n                    recognitionInstance.interimResults = false;\n                    recognitionInstance.lang = 'en-US';\n                    // Handle results\n                    recognitionInstance.onresult = ({\n                        \"QnAWithHeyGenModal.useEffect\": (event)=>{\n                            const transcript = event.results[0][0].transcript;\n                            setTaskInput(transcript);\n                            updateStatus('\\uD83C\\uDFA4 Voice input: \"'.concat(transcript, '\"'), \"success\");\n                        }\n                    })[\"QnAWithHeyGenModal.useEffect\"];\n                    // Handle errors\n                    recognitionInstance.onerror = ({\n                        \"QnAWithHeyGenModal.useEffect\": (event)=>{\n                            updateStatus(\"Voice recognition error: \".concat(event.error), \"error\");\n                            setIsRecording(false);\n                        }\n                    })[\"QnAWithHeyGenModal.useEffect\"];\n                    // Handle end\n                    recognitionInstance.onend = ({\n                        \"QnAWithHeyGenModal.useEffect\": ()=>{\n                            setIsRecording(false);\n                        }\n                    })[\"QnAWithHeyGenModal.useEffect\"];\n                    setRecognition(recognitionInstance);\n                } else {\n                    setIsVoiceSupported(false);\n                    updateStatus(\"Voice input not supported in this browser\", \"warning\");\n                }\n            }\n        }\n    }[\"QnAWithHeyGenModal.useEffect\"], []);\n    // Get session token\n    const getSessionToken = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.create_token\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"X-Api-Key\": API_CONFIG.apiKey\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            if (!data.data || !data.data.token) {\n                throw new Error('Invalid token data received from server');\n            }\n            setSessionToken(data.data.token);\n            updateStatus(\"Session token obtained successfully\", \"success\");\n            return data.data.token;\n        } catch (error) {\n            updateStatus(\"Failed to get session token: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Connect WebSocket\n    const connectWebSocket = async (sessionId, token)=>{\n        try {\n            if (!token) {\n                throw new Error('No session token available for WebSocket connection');\n            }\n            const params = new URLSearchParams({\n                session_id: sessionId,\n                session_token: token,\n                silence_response: 'false',\n                opening_text: \"Hello, how can I help you?\",\n                stt_language: \"en\"\n            });\n            const wsUrl = \"wss://\".concat(new URL(API_CONFIG.serverUrl).hostname, \"/v1/ws/streaming.chat?\").concat(params);\n            const ws = new WebSocket(wsUrl);\n            setWebSocket(ws);\n            ws.addEventListener(\"message\", (event)=>{\n                const eventData = JSON.parse(event.data);\n                console.log(\"Raw WebSocket event:\", eventData);\n            });\n            ws.addEventListener(\"error\", (error)=>{\n                updateStatus(\"WebSocket connection error\", \"error\");\n                console.error(\"WebSocket error:\", error);\n            });\n            ws.addEventListener(\"open\", ()=>{\n                updateStatus(\"WebSocket connected successfully\", \"success\");\n            });\n        } catch (error) {\n            updateStatus(\"WebSocket connection failed: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Create new session\n    const createNewSession = async ()=>{\n        try {\n            // Always get a fresh token to avoid state timing issues\n            const token = await getSessionToken();\n            updateStatus(\"Debug: Creating session with token: \".concat(token ? 'Token exists' : 'No token'), \"info\");\n            const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.new\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    quality: \"high\",\n                    avatar_name: avatarID,\n                    voice: {\n                        voice_id: voiceID,\n                        rate: 1.0\n                    },\n                    version: \"v2\",\n                    video_encoding: \"H264\"\n                })\n            });\n            updateStatus(\"Debug: Create session response status: \".concat(response.status), \"info\");\n            if (!response.ok) {\n                const errorText = await response.text();\n                updateStatus(\"Debug: Create session error: \".concat(errorText), \"error\");\n                throw new Error(\"HTTP error! status: \".concat(response.status, \" - \").concat(errorText));\n            }\n            const data = await response.json();\n            if (!data.data || !data.data.session_id) {\n                throw new Error('Invalid session data received from server');\n            }\n            updateStatus(\"Debug: Session data keys: \".concat(Object.keys(data.data).join(', ')), \"info\");\n            // Set session info and return it for immediate use\n            setSessionInfo(data.data);\n            // Create LiveKit Room\n            const livekitRoom = new livekit_client__WEBPACK_IMPORTED_MODULE_2__.Room({\n                adaptiveStream: true,\n                dynacast: true,\n                videoCaptureDefaults: {\n                    resolution: livekit_client__WEBPACK_IMPORTED_MODULE_2__.VideoPresets.h720.resolution\n                }\n            });\n            setRoom(livekitRoom);\n            // Handle room events\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.DataReceived, (message)=>{\n                const data = new TextDecoder().decode(message);\n                console.log(\"Room message:\", JSON.parse(data));\n            });\n            // Handle media streams\n            const newMediaStream = new MediaStream();\n            setMediaStream(newMediaStream);\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.TrackSubscribed, (track)=>{\n                if (track.kind === \"video\" || track.kind === \"audio\") {\n                    newMediaStream.addTrack(track.mediaStreamTrack);\n                    if (newMediaStream.getVideoTracks().length > 0 && newMediaStream.getAudioTracks().length > 0) {\n                        if (mediaElementRef.current) {\n                            mediaElementRef.current.srcObject = newMediaStream;\n                            updateStatus(\"Media stream ready - Avatar is live!\", \"success\");\n                        }\n                    }\n                }\n            });\n            // Handle media stream removal\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.TrackUnsubscribed, (track)=>{\n                const mediaTrack = track.mediaStreamTrack;\n                if (mediaTrack) {\n                    newMediaStream.removeTrack(mediaTrack);\n                }\n            });\n            // Handle room connection state changes\n            livekitRoom.on(livekit_client__WEBPACK_IMPORTED_MODULE_2__.RoomEvent.Disconnected, (reason)=>{\n                updateStatus(\"Room disconnected: \".concat(reason), \"warning\");\n            });\n            await livekitRoom.prepareConnection(data.data.url, data.data.access_token);\n            updateStatus(\"Connection prepared successfully\", \"success\");\n            await connectWebSocket(data.data.session_id, token);\n            updateStatus(\"Session created successfully\", \"success\");\n            // Return both session data, token, and room for immediate use\n            return {\n                sessionData: data.data,\n                token,\n                room: livekitRoom\n            };\n        } catch (error) {\n            updateStatus(\"Failed to create session: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Start streaming session\n    const startStreamingSession = async (sessionData, token, livekitRoom)=>{\n        if (!sessionData || !sessionData.session_id) {\n            throw new Error('No session info available');\n        }\n        try {\n            updateStatus(\"Debug: Using token: \".concat(token ? 'Token exists' : 'No token'), \"info\");\n            updateStatus(\"Debug: Session ID: \".concat(sessionData.session_id), \"info\");\n            const startResponse = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.start\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    session_id: sessionData.session_id\n                })\n            });\n            updateStatus(\"Debug: Start response status: \".concat(startResponse.status), \"info\");\n            if (!startResponse.ok) {\n                const errorText = await startResponse.text();\n                updateStatus(\"Debug: Error response: \".concat(errorText), \"error\");\n                throw new Error(\"HTTP error! status: \".concat(startResponse.status, \" - \").concat(errorText));\n            }\n            // Connect to LiveKit room\n            updateStatus(\"Debug: Available session properties: \".concat(Object.keys(sessionData).join(', ')), \"info\");\n            updateStatus(\"Debug: Room exists: \".concat(!!livekitRoom), \"info\");\n            updateStatus(\"Debug: URL exists: \".concat(!!sessionData.url), \"info\");\n            updateStatus(\"Debug: Access token exists: \".concat(!!sessionData.access_token), \"info\");\n            if (livekitRoom && sessionData.url && sessionData.access_token) {\n                await livekitRoom.connect(sessionData.url, sessionData.access_token);\n                updateStatus(\"Connected to room successfully\", \"success\");\n            } else {\n                updateStatus(\"Warning: Room or connection details missing\", \"warning\");\n                updateStatus(\"Debug: Missing - Room: \".concat(!livekitRoom, \", URL: \").concat(!sessionData.url, \", Token: \").concat(!sessionData.access_token), \"error\");\n            }\n            setIsStarted(true);\n            updateStatus(\"Streaming started successfully\", \"success\");\n        } catch (error) {\n            updateStatus(\"Failed to start streaming: \".concat(error.message), \"error\");\n            throw error;\n        }\n    };\n    // Send text to avatar\n    const sendText = async function(text) {\n        let taskType = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"talk\";\n        if (!sessionInfo) {\n            updateStatus(\"No active session - please start the avatar first\", \"warning\");\n            return;\n        }\n        const token = sessionToken;\n        if (!token) {\n            updateStatus(\"No session token available\", \"error\");\n            return;\n        }\n        try {\n            updateStatus(\"Sending message to avatar...\", \"info\");\n            const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.task\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    session_id: sessionInfo.session_id,\n                    text: text,\n                    task_type: taskType\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || data.error) {\n                updateStatus(\"HeyGen API error: \".concat(data.error || response.statusText), \"error\");\n            } else {\n                updateStatus(\"✓ Message sent successfully (\".concat(taskType, \")\"), \"success\");\n                updateStatus('Avatar will speak: \"'.concat(text.substring(0, 50)).concat(text.length > 50 ? '...' : '', '\"'), \"info\");\n            }\n        } catch (err) {\n            updateStatus(\"HeyGen API call failed: \" + err.message, \"error\");\n        }\n    };\n    // Close session\n    const closeSession = async ()=>{\n        if (!sessionInfo) {\n            updateStatus(\"No active session\", \"warning\");\n            return;\n        }\n        try {\n            const token = sessionToken;\n            if (token) {\n                const response = await fetch(\"\".concat(API_CONFIG.serverUrl, \"/v1/streaming.stop\"), {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        Authorization: \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        session_id: sessionInfo.session_id\n                    })\n                });\n            }\n            // Close WebSocket\n            if (webSocket) {\n                webSocket.close();\n            }\n            // Disconnect from LiveKit room\n            if (room) {\n                room.disconnect();\n            }\n            // Reset states\n            if (mediaElementRef.current) {\n                mediaElementRef.current.srcObject = null;\n            }\n            setSessionInfo(null);\n            setRoom(null);\n            setMediaStream(null);\n            setSessionToken(null);\n            setIsStarted(false);\n            updateStatus(\"Session closed successfully\", \"warning\");\n        } catch (error) {\n            updateStatus(\"Error closing session: \".concat(error.message), \"error\");\n            // Still reset states even if API call fails\n            setSessionInfo(null);\n            setRoom(null);\n            setMediaStream(null);\n            setSessionToken(null);\n            setIsStarted(false);\n        }\n    };\n    // Azure Search and OpenAI integration\n    const callAzureOpenAI = async (prompt)=>{\n        try {\n            var _data_choices__message, _data_choices_, _data_choices;\n            const response = await fetch(\"http://localhost:5001/api/openai\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    prompt\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || !data.choices) {\n                const errorMsg = data.error ? typeof data.error === 'object' ? JSON.stringify(data.error) : data.error : response.statusText;\n                updateStatus(\"Azure OpenAI error: \" + errorMsg, \"error\");\n                return null;\n            }\n            return (_data_choices = data.choices) === null || _data_choices === void 0 ? void 0 : (_data_choices_ = _data_choices[0]) === null || _data_choices_ === void 0 ? void 0 : (_data_choices__message = _data_choices_.message) === null || _data_choices__message === void 0 ? void 0 : _data_choices__message.content;\n        } catch (err) {\n            updateStatus(\"Azure OpenAI call failed: \" + err.message, \"error\");\n            return null;\n        }\n    };\n    const searchAzure = async (query)=>{\n        try {\n            const response = await fetch(\"http://localhost:5001/api/search\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    query,\n                    courseId\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || !data.value) {\n                const errorMsg = data.error ? typeof data.error === 'object' ? JSON.stringify(data.error) : data.error : response.statusText;\n                updateStatus(\"Azure Search error: \" + errorMsg, \"error\");\n                return null;\n            }\n            updateStatus(\"Found \".concat(data.value.length, \" relevant documents\"), \"success\");\n            return (data.value || []).map((r)=>r.content).join('\\n\\n');\n        } catch (err) {\n            updateStatus(\"Azure Search call failed: \" + err.message, \"error\");\n            return null;\n        }\n    };\n    // Handle Ask AI button\n    const handleAskAI = async ()=>{\n        const text = taskInput.trim();\n        if (!text) {\n            updateStatus(\"Please enter a question first\", \"warning\");\n            return;\n        }\n        if (!sessionInfo) {\n            updateStatus(\"Please start the avatar session first\", \"warning\");\n            return;\n        }\n        try {\n            updateStatus('\\uD83D\\uDD0D Searching for: \"'.concat(text, '\"'), \"info\");\n            const searchResults = await searchAzure(text);\n            if (!searchResults) {\n                updateStatus(\"No relevant documents found for your question\", \"warning\");\n                return;\n            }\n            updateStatus(\"🤖 Processing with AI...\", \"info\");\n            const llmAnswer = await callAzureOpenAI('Based on the following context, answer the question: \"'.concat(text, '\"\\n\\nContext: ').concat(searchResults));\n            if (llmAnswer) {\n                updateStatus(\"\\uD83D\\uDCA1 AI Response: \".concat(llmAnswer.substring(0, 100)).concat(llmAnswer.length > 100 ? '...' : ''), \"success\");\n                // Clean the response for avatar speech\n                let cleaned = llmAnswer.replace(/[#*_`>-]+/g, '').replace(/\\n{2,}/g, ' ').replace(/\\s{2,}/g, ' ').trim();\n                updateStatus(\"\\uD83C\\uDFA4 Avatar speaking response...\", \"info\");\n                await sendText(cleaned, \"repeat\");\n                setTaskInput(\"\");\n            } else {\n                updateStatus(\"Failed to generate AI response\", \"error\");\n            }\n        } catch (error) {\n            updateStatus(\"Error processing question: \".concat(error.message), \"error\");\n        }\n    };\n    // Handle Voice Input button\n    const handleVoiceInput = ()=>{\n        if (!isVoiceSupported) {\n            updateStatus(\"Voice input not supported in this browser\", \"warning\");\n            return;\n        }\n        if (!recognition) {\n            updateStatus(\"Voice recognition not initialized\", \"error\");\n            return;\n        }\n        if (isRecording) {\n            // Stop recording\n            recognition.stop();\n            setIsRecording(false);\n            updateStatus(\"Voice recording stopped\", \"info\");\n        } else {\n            // Start recording\n            try {\n                recognition.start();\n                setIsRecording(true);\n                updateStatus(\"🎤 Listening... Speak your question\", \"info\");\n            } catch (error) {\n                updateStatus(\"Failed to start voice recording: \".concat(error.message), \"error\");\n                setIsRecording(false);\n            }\n        }\n    };\n    // Handle Start button\n    const handleStart = async ()=>{\n        try {\n            const result = await createNewSession();\n            await startStreamingSession(result.sessionData, result.token, result.room);\n        } catch (error) {\n            updateStatus(\"Error starting session: \".concat(error.message), \"error\");\n        }\n    };\n    // Handle Enter key in textarea\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleAskAI();\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-white\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 598,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold\",\n                                                children: \"AI Avatar Assistant\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white text-opacity-70 text-sm\",\n                                                children: \"Powered by EXL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full \".concat(isStarted ? 'bg-green-400 animate-pulse' : 'bg-gray-400')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-opacity-80 text-sm font-medium\",\n                                                children: isStarted ? 'Live' : 'Offline'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        className: \"text-white hover:text-gray-200 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 617,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 606,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                        lineNumber: 594,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                    lineNumber: 593,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 max-h-[calc(90vh-120px)] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-2xl p-6 border border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-800 mb-6 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2 text-blue-600\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 634,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Avatar Configuration\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                                children: \"Avatar ID\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 642,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: \"Enter Avatar ID\",\n                                                                value: avatarID,\n                                                                onChange: (e)=>setAvatarID(e.target.value),\n                                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 643,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 641,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-3 mt-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleStart,\n                                                                disabled: true,\n                                                                className: \"px-4 py-3 bg-green-500 hover:bg-green-600 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none shadow-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 inline mr-2\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 671,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 670,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    isStarted ? 'Connected' : 'Auto-Starting...'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 665,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: closeSession,\n                                                                className: \"px-4 py-3 bg-red-500 hover:bg-red-600 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 inline mr-2\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 680,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 679,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \"Stop\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 675,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-2xl p-6 border border-gray-200 mt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-800 mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 mr-2 text-cyan-600\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Ask Your Question\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 690,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            placeholder: \"Type your question here...\",\n                                                            value: taskInput,\n                                                            onChange: (e)=>setTaskInput(e.target.value),\n                                                            onKeyDown: handleKeyDown,\n                                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-200 resize-none h-24\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleAskAI,\n                                                                className: \"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 inline mr-2\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 714,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 713,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    \"Ask AI\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 709,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleVoiceInput,\n                                                                disabled: !isVoiceSupported,\n                                                                className: \"px-4 py-3 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg \".concat(isRecording ? 'bg-red-500 hover:bg-red-600 animate-pulse' : isVoiceSupported ? 'bg-purple-500 hover:bg-purple-600' : 'bg-gray-400 cursor-not-allowed'),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 inline mr-2\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a2 2 0 114 0v4a2 2 0 11-4 0V7z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 731,\n                                                                            columnNumber: 27\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                            lineNumber: 733,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                        lineNumber: 729,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    isRecording ? 'Stop Recording' : 'Voice Input'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 718,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 708,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 697,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 630,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-2xl p-6 border border-gray-200 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-semibold text-gray-800 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 mr-2 text-blue-600\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M2 6a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 750,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 749,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"AI Avatar\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 755,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Ready\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                            lineNumber: 756,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 747,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                ref: mediaElementRef,\n                                                className: \"w-full h-96 bg-gray-900 rounded-xl object-cover shadow-lg\",\n                                                autoPlay: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-16 h-16 mx-auto mb-4 opacity-50\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                    lineNumber: 769,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 768,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-medium\",\n                                                                children: \"Avatar will appear here\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 771,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm opacity-75\",\n                                                                children: \"Click Start to begin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                                lineNumber: 772,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                                lineNumber: 761,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                            lineNumber: 760,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                                lineNumber: 744,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                        lineNumber: 627,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n                    lineNumber: 626,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n            lineNumber: 591,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\components\\\\QnAWithHeyGenModal.tsx\",\n        lineNumber: 590,\n        columnNumber: 5\n    }, undefined);\n};\n_s(QnAWithHeyGenModal, \"KD4zp0K+Vr4yX6MzZrH14V/PMpU=\");\n_c = QnAWithHeyGenModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QnAWithHeyGenModal);\nvar _c;\n$RefreshReg$(_c, \"QnAWithHeyGenModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/QnAWithHeyGenModal.tsx\n"));

/***/ })

});