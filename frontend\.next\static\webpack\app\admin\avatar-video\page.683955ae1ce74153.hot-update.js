"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/avatar-video/page",{

/***/ "(app-pages-browser)/./src/app/admin/AvatarVideoPanel.tsx":
/*!********************************************!*\
  !*** ./src/app/admin/AvatarVideoPanel.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AvatarVideoPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clapperboard.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-user-round.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction AvatarVideoPanel() {\n    var _avatars_find, _uniqueCourses_find, _filteredVersions_find;\n    _s();\n    const [avatars, setAvatars] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]);\n    const [selectedAvatarId, setSelectedAvatarId] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null);\n    const [approvedVersions, setApprovedVersions] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]);\n    const [selectedCourseIdForVideo, setSelectedCourseIdForVideo] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null);\n    const [selectedVersionId, setSelectedVersionId] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null);\n    const [loadingVersions, setLoadingVersions] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(true);\n    const [loadingAvatars, setLoadingAvatars] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(true);\n    const [generatingVideos, setGeneratingVideos] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false);\n    const [videoGenerationResults, setVideoGenerationResults] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]);\n    const [showConfirmDialog, setShowConfirmDialog] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false);\n    const [generationProgress, setGenerationProgress] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)({\n        current: 0,\n        total: 0\n    });\n    const [currentGeneratingPage, setCurrentGeneratingPage] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)('');\n    // Auto-generation states\n    const [autoStartCountdown, setAutoStartCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null);\n    const [hasAutoStarted, setHasAutoStarted] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false);\n    const countdownIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_8__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_8__.useEffect)({\n        \"AvatarVideoPanel.useEffect\": ()=>{\n            const fetchApprovedVersions = {\n                \"AvatarVideoPanel.useEffect.fetchApprovedVersions\": async ()=>{\n                    try {\n                        const res = await fetch(\"http://localhost:5001/api/all-approved-content-versions\");\n                        const data = await res.json();\n                        setApprovedVersions(data);\n                    } catch (err) {\n                        console.error(\"Failed to fetch approved versions:\", err);\n                        alert(\"Failed to load approved content versions.\");\n                    } finally{\n                        setLoadingVersions(false);\n                    }\n                }\n            }[\"AvatarVideoPanel.useEffect.fetchApprovedVersions\"];\n            const fetchAvatars = {\n                \"AvatarVideoPanel.useEffect.fetchAvatars\": async ()=>{\n                    try {\n                        const res = await fetch(\"http://localhost:5001/api/avatars\");\n                        const data = await res.json();\n                        if (Array.isArray(data)) {\n                            setAvatars(data);\n                        } else {\n                            console.error(\"API returned non-array data for avatars:\", data);\n                            setAvatars([]);\n                            alert(\"Failed to load avatar configurations: Invalid data format.\");\n                        }\n                    } catch (err) {\n                        console.error(\"Failed to fetch avatars:\", err);\n                        alert(\"Failed to load avatar configurations. Please ensure backend is running.\");\n                    } finally{\n                        setLoadingAvatars(false);\n                    }\n                }\n            }[\"AvatarVideoPanel.useEffect.fetchAvatars\"];\n            fetchApprovedVersions();\n            fetchAvatars();\n        }\n    }[\"AvatarVideoPanel.useEffect\"], []);\n    // Auto-generation effect - triggers when both avatar and version are selected\n    (0,react__WEBPACK_IMPORTED_MODULE_8__.useEffect)({\n        \"AvatarVideoPanel.useEffect\": ()=>{\n            // Clear any existing countdown\n            if (countdownIntervalRef.current) {\n                clearInterval(countdownIntervalRef.current);\n                countdownIntervalRef.current = null;\n            }\n            // Only auto-start if both selections are made and we haven't already auto-started\n            if (selectedAvatarId && selectedVersionId && !hasAutoStarted && !generatingVideos && videoGenerationResults.length === 0) {\n                console.log(\"Both avatar and version selected, starting auto-generation countdown...\");\n                // Start countdown from 3 seconds\n                setAutoStartCountdown(3);\n                countdownIntervalRef.current = setInterval({\n                    \"AvatarVideoPanel.useEffect\": ()=>{\n                        setAutoStartCountdown({\n                            \"AvatarVideoPanel.useEffect\": (prev)=>{\n                                if (prev === null || prev <= 1) {\n                                    // Countdown finished, start generation\n                                    if (countdownIntervalRef.current) {\n                                        clearInterval(countdownIntervalRef.current);\n                                        countdownIntervalRef.current = null;\n                                    }\n                                    setHasAutoStarted(true);\n                                    setAutoStartCountdown(null);\n                                    // Trigger generation directly without confirmation dialog\n                                    handleConfirmGeneration();\n                                    return null;\n                                }\n                                return prev - 1;\n                            }\n                        }[\"AvatarVideoPanel.useEffect\"]);\n                    }\n                }[\"AvatarVideoPanel.useEffect\"], 1000);\n            } else {\n                setAutoStartCountdown(null);\n            }\n            // Cleanup interval on unmount or dependency change\n            return ({\n                \"AvatarVideoPanel.useEffect\": ()=>{\n                    if (countdownIntervalRef.current) {\n                        clearInterval(countdownIntervalRef.current);\n                        countdownIntervalRef.current = null;\n                    }\n                }\n            })[\"AvatarVideoPanel.useEffect\"];\n        }\n    }[\"AvatarVideoPanel.useEffect\"], [\n        selectedAvatarId,\n        selectedVersionId,\n        hasAutoStarted,\n        generatingVideos,\n        videoGenerationResults.length\n    ]);\n    // Reset auto-start flag when selections change\n    (0,react__WEBPACK_IMPORTED_MODULE_8__.useEffect)({\n        \"AvatarVideoPanel.useEffect\": ()=>{\n            if (!selectedAvatarId || !selectedVersionId) {\n                setHasAutoStarted(false);\n                setAutoStartCountdown(null);\n            }\n        }\n    }[\"AvatarVideoPanel.useEffect\"], [\n        selectedAvatarId,\n        selectedVersionId\n    ]);\n    const cancelAutoStart = ()=>{\n        if (countdownIntervalRef.current) {\n            clearInterval(countdownIntervalRef.current);\n            countdownIntervalRef.current = null;\n        }\n        setAutoStartCountdown(null);\n        setHasAutoStarted(true); // Prevent auto-start from happening again\n    };\n    const uniqueCourses = approvedVersions.reduce((acc, version)=>{\n        if (!acc.find((course)=>course.course_id === version.course_id)) {\n            acc.push({\n                course_id: version.course_id,\n                course_title: version.course_title\n            });\n        }\n        return acc;\n    }, []);\n    const filteredVersions = approvedVersions.filter((version)=>version.course_id === Number(selectedCourseIdForVideo));\n    const handleGenerateVideosClick = ()=>{\n        if (!selectedAvatarId || !selectedVersionId) {\n            alert(\"Please select both an avatar and a content version.\");\n            return;\n        }\n        setShowConfirmDialog(true);\n    };\n    const handleConfirmGeneration = async ()=>{\n        setShowConfirmDialog(false);\n        setGeneratingVideos(true);\n        setVideoGenerationResults([]);\n        setGenerationProgress({\n            current: 0,\n            total: 0\n        });\n        setCurrentGeneratingPage('');\n        try {\n            // First get the content to know total pages\n            const versionRes = await fetch(\"http://localhost:5001/api/course-content-versions/\".concat(selectedVersionId));\n            const versionData = await versionRes.json();\n            const totalPages = Array.isArray(versionData.content) ? versionData.content.length : 0;\n            setGenerationProgress({\n                current: 0,\n                total: totalPages\n            });\n            const res = await fetch(\"http://localhost:5001/api/generate-course-videos\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    selectedAvatarId,\n                    selectedVersionId\n                })\n            });\n            const result = await res.json();\n            if (!res.ok) {\n                throw new Error(result.error || \"Failed to initiate video generation\");\n            }\n            console.log(\"Generated Videos Data:\", result.generated_videos);\n            setVideoGenerationResults(result.generated_videos || []);\n        } catch (error) {\n            console.error(\"Video generation error:\", error);\n            alert(\"Video generation failed: \".concat(error.message || \"Unknown error\"));\n        } finally{\n            setGeneratingVideos(false);\n            setCurrentGeneratingPage('');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"mb-8 border-0 shadow-xl bg-white/80 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20 h-20 bg-orange-400 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-10 h-10 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold mb-3 text-gray-900 bg-brand-blue bg-clip-text text-transparent\",\n                                    children: \"Medical Course Avatar & Video Generation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n                                    children: \"Select a medical professional avatar and generate engaging videos from your approved summaries with cutting-edge AI technology\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {\n                            className: \"my-8\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"p-4 transition-all duration-300 \".concat(selectedAvatarId ? 'border-blue-500 bg-blue-50 shadow-lg scale-105' : 'border-gray-200 hover:shadow-md'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-xl flex items-center justify-center transition-colors \".concat(selectedAvatarId ? 'bg-blue-500' : 'bg-gray-200'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-6 h-6 \".concat(selectedAvatarId ? 'text-white' : 'text-gray-400')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold \".concat(selectedAvatarId ? 'text-blue-600' : 'text-gray-500'),\n                                                        children: \"Select Avatar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Choose your presenter\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"p-4 transition-all duration-300 \".concat(selectedVersionId ? 'border-green-500 bg-green-50 shadow-lg scale-105' : 'border-gray-200 hover:shadow-md'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-xl flex items-center justify-center transition-colors \".concat(selectedVersionId ? 'bg-green-500' : 'bg-gray-200'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-6 h-6 \".concat(selectedVersionId ? 'text-white' : 'text-gray-400')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold \".concat(selectedVersionId ? 'text-green-600' : 'text-gray-500'),\n                                                        children: \"Select Content\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Choose course material\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"p-4 transition-all duration-300 \".concat(generatingVideos ? 'border-amber-500 bg-amber-50 shadow-lg scale-105' : 'border-gray-200 hover:shadow-md'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-xl flex items-center justify-center transition-colors \".concat(generatingVideos ? 'bg-amber-500' : 'bg-gray-200'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-6 h-6 \".concat(generatingVideos ? 'text-white' : 'text-gray-400')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold \".concat(generatingVideos ? 'text-amber-600' : 'text-gray-500'),\n                                                        children: \"Generate Videos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Create content\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"mb-8 border-0 shadow-xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        className: \"pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-7 h-7 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"text-2xl text-gray-900\",\n                                            children: \"Select Medical Professional Avatar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mt-1\",\n                                            children: \"Choose your video presenter from our collection of medical professionals\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loadingAvatars ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"Loading avatars...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this) : avatars.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"p-12 text-center bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDC65\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 text-lg\",\n                                    children: \"No avatars available.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                            children: avatars.map((a)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"group relative cursor-pointer transition-all duration-300 hover:shadow-xl \".concat(selectedAvatarId === a.id ? \"border-2 border-blue-500 bg-blue-50 shadow-xl scale-105\" : \"border hover:border-blue-300 hover:bg-blue-50/50 hover:scale-102\"),\n                                    onClick: ()=>setSelectedAvatarId(a.id),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"p-6 text-center\",\n                                        style: {\n                                            minHeight: 280\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center transition-all \".concat(selectedAvatarId === a.id ? 'bg-blue-500 shadow-lg' : 'bg-orange-100 group-hover:bg-blue-100'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-8 h-8 transition-colors \".concat(selectedAvatarId === a.id ? 'text-white' : 'text-orange-600 group-hover:text-blue-600')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-gray-900 mb-3 text-lg truncate\",\n                                                title: a.avatar_name,\n                                                children: a.avatar_name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"text-xs\",\n                                                        children: a.specialty || \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500 truncate\",\n                                                        title: a.domain || \"N/A\",\n                                                        children: a.domain || \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-orange-600 truncate\",\n                                                        title: a.voice_id || \"N/A\",\n                                                        children: [\n                                                            \"Voice: \",\n                                                            a.voice_id || \"N/A\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: selectedAvatarId === a.id ? \"default\" : \"outline\",\n                                                className: \"w-full transition-all duration-200 \".concat(selectedAvatarId === a.id ? \"bg-blue-600 hover:bg-blue-700 text-white shadow-md\" : \"hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300\"),\n                                                children: selectedAvatarId === a.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        \"Selected\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 25\n                                                }, this) : \"Select Avatar\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 19\n                                    }, this)\n                                }, a.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this),\n            selectedAvatarId !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"mb-8 border-0 shadow-xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        className: \"pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-7 h-7 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"text-2xl text-gray-900\",\n                                            children: \"Select Course Content for Video Generation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mt-1\",\n                                            children: \"Choose your course and approved version for video creation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loadingVersions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-2 border-gray-300 border-t-green-500 rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"Loading approved content versions...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 15\n                        }, this) : approvedVersions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"p-12 text-center bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDCDA\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 text-lg\",\n                                    children: \"No approved content versions available.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_2__.Label, {\n                                            htmlFor: \"course\",\n                                            className: \"text-base font-semibold text-gray-700\",\n                                            children: \"Course Selection\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                            value: selectedCourseIdForVideo || '',\n                                            onValueChange: (value)=>{\n                                                setSelectedCourseIdForVideo(value);\n                                                setSelectedVersionId(null);\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                                    className: \"w-full h-14 text-base focus:ring-2 focus:ring-green-200 border-2 rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                                        placeholder: \"Choose a course to generate videos from\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                                    children: uniqueCourses.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                            value: course.course_id.toString(),\n                                                            className: \"text-base py-3\",\n                                                            children: course.course_title\n                                                        }, course.course_id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 17\n                                }, this),\n                                selectedCourseIdForVideo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_2__.Label, {\n                                            htmlFor: \"version\",\n                                            className: \"text-base font-semibold text-gray-700\",\n                                            children: \"Version Selection\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                            value: selectedVersionId || '',\n                                            onValueChange: (value)=>{\n                                                setSelectedVersionId(value);\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                                    className: \"w-full h-14 text-base focus:ring-2 focus:ring-green-200 border-2 rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                                        placeholder: \"Select an approved version\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                                    children: filteredVersions.map((version)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                            value: version.version_id.toString(),\n                                                            className: \"text-base py-3\",\n                                                            children: [\n                                                                \"Version \",\n                                                                version.version_number,\n                                                                \" (Approved: \",\n                                                                new Date(version.approved_at).toLocaleDateString(),\n                                                                \")\"\n                                                            ]\n                                                        }, version.version_id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 346,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"border-0 shadow-xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        className: \"pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-amber-100 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-7 h-7 text-amber-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"text-2xl text-gray-900\",\n                                                    children: \"Video Generation Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mt-1\",\n                                                    children: \"Generate professional video content from your selections\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: 'warning',\n                                    size: \"lg\",\n                                    className: \"flex items-center space-x-3 px-8 py-4 text-white font-semibold rounded-2xl shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105\",\n                                    onClick: handleGenerateVideosClick,\n                                    disabled: !selectedAvatarId || !selectedVersionId || generatingVideos,\n                                    children: generatingVideos ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"animate-spin w-6 h-6 text-white\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                        className: \"opacity-25\",\n                                                        cx: \"12\",\n                                                        cy: \"12\",\n                                                        r: \"10\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        className: \"opacity-75\",\n                                                        fill: \"currentColor\",\n                                                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: \"Generating Videos...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: selectedAvatarId && selectedVersionId && !hasAutoStarted ? \"Start Now (Skip Auto-Start)\" : \"Generate All Videos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: [\n                            generatingVideos && generationProgress.total > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"mb-8 bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-blue-900 text-lg flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-5 h-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Generation Progress\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"bg-blue-100 text-blue-800 font-semibold\",\n                                                    children: [\n                                                        generationProgress.current,\n                                                        \" of \",\n                                                        generationProgress.total,\n                                                        \" completed\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_6__.Progress, {\n                                            value: generationProgress.current / generationProgress.total * 100,\n                                            className: \"h-3 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 17\n                                        }, this),\n                                        currentGeneratingPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-700 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Currently generating: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium ml-1\",\n                                                    children: currentGeneratingPage\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 43\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 13\n                            }, this),\n                            videoGenerationResults.length === 0 && !generatingVideos && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-32 h-32 bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-16 h-16 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                        children: \"Ready to Generate Videos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 max-w-lg mx-auto mb-8 text-lg leading-relaxed\",\n                                        children: \"Select an avatar and course content to automatically begin creating your professional video content with AI. Generation will start automatically once both selections are made.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 15\n                                    }, this),\n                                    (!selectedAvatarId || !selectedVersionId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-200 max-w-md mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2 text-amber-700 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-lg\",\n                                                            children: \"Selection Required\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-amber-600\",\n                                                    children: \"Please select both an avatar and course content to proceed with video generation.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 17\n                                    }, this),\n                                    selectedAvatarId && selectedVersionId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            autoStartCountdown !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-300 max-w-md mx-auto mb-4 animate-pulse\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center space-x-3 text-blue-700 mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-xl\",\n                                                                            children: autoStartCountdown\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 527,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute inset-0 w-12 h-12 border-4 border-blue-300 rounded-full animate-spin border-t-transparent\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 530,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 526,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-semibold text-lg block\",\n                                                                            children: \"Auto-Starting Generation\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-blue-600\",\n                                                                            children: [\n                                                                                \"Starting in \",\n                                                                                autoStartCountdown,\n                                                                                \" seconds...\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 534,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"w-full border-blue-300 text-blue-700 hover:bg-blue-50\",\n                                                            onClick: cancelAutoStart,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 543,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Cancel Auto-Start\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 21\n                                            }, this),\n                                            autoStartCountdown === null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                className: \"bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200 max-w-md mx-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center space-x-2 text-green-700 mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-6 h-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold text-lg\",\n                                                                    children: hasAutoStarted ? \"Ready to Generate!\" : \"Auto-Generation Ready!\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 556,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-green-600 text-center\",\n                                                            children: hasAutoStarted ? \"Avatar and content selected. Click the button above to start video generation.\" : \"Avatar and content selected. Generation will start automatically in a moment, or click the button above to start immediately.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 13\n                            }, this),\n                            videoGenerationResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-6 h-6 text-gray-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-bold text-gray-800 text-xl\",\n                                                children: \"Generated Videos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 579,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"bg-green-100 text-green-800\",\n                                                children: [\n                                                    videoGenerationResults.length,\n                                                    \" videos processed\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-4\",\n                                        children: videoGenerationResults.map((page, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                className: \"hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center mb-4\",\n                                                                    children: [\n                                                                        page.generation_status === \"success\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 bg-green-500 rounded-full mr-4 flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-white\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                                lineNumber: 593,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 592,\n                                                                            columnNumber: 31\n                                                                        }, this) : page.generation_status === \"skipped_empty_script\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 bg-yellow-500 rounded-full mr-4 flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-white\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                                lineNumber: 597,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 596,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 bg-red-500 rounded-full mr-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 600,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                            className: \"font-bold text-gray-800 text-xl\",\n                                                                            children: page.title || \"Chapter \".concat(page.page)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 602,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 590,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"mb-4\",\n                                                                    children: [\n                                                                        \"Page \",\n                                                                        page.page\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 606,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                page.generation_status === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: \"bg-green-100 text-green-800 hover:bg-green-100\",\n                                                                    children: \"✅ Generated Successfully\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 611,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                page.generation_status === \"skipped_empty_script\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: \"bg-yellow-100 text-yellow-800 hover:bg-yellow-100\",\n                                                                    children: \"⚠️ Skipped (Empty Script)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 617,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                page.generation_status === \"failed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            className: \"bg-red-100 text-red-800 hover:bg-red-100\",\n                                                                            children: \"❌ Generation Failed\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 624,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-red-600 mt-2 p-3 bg-red-50 rounded-lg border border-red-200\",\n                                                                            children: page.error_details || \"Unknown error occurred during generation\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 627,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 623,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 589,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 576,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 424,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: showConfirmDialog,\n                onOpenChange: setShowConfirmDialog,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    className: \"max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20 h-20 bg-gradient-to-br from-amber-400 to-orange-500 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-10 h-10 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                    className: \"text-2xl font-bold text-gray-800 mb-4\",\n                                    children: \"Generate All Videos?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 650,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-6 text-lg leading-relaxed\",\n                                        children: [\n                                            \"This will generate videos for all pages in the selected course content.\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 653,\n                                                columnNumber: 88\n                                            }, this),\n                                            \"This process may take several minutes to complete.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 651,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 646,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-200 mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center font-bold mb-4 text-amber-900 text-lg\",\n                                        children: \"Selected Configuration\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-6 h-6 text-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 665,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-amber-800\",\n                                                            children: \"Avatar\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 666,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-amber-700 break-words\",\n                                                            children: (_avatars_find = avatars.find((a)=>a.id === selectedAvatarId)) === null || _avatars_find === void 0 ? void 0 : _avatars_find.avatar_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 667,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 663,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-6 h-6 text-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 674,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-amber-800\",\n                                                            children: \"Course\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 675,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-amber-700 break-words\",\n                                                            children: (_uniqueCourses_find = uniqueCourses.find((c)=>c.course_id === Number(selectedCourseIdForVideo))) === null || _uniqueCourses_find === void 0 ? void 0 : _uniqueCourses_find.course_title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 676,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-6 h-6 text-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 683,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-amber-800\",\n                                                            children: \"Version\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 684,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-amber-700 break-words\",\n                                                            children: (_filteredVersions_find = filteredVersions.find((v)=>v.version_id === selectedVersionId)) === null || _filteredVersions_find === void 0 ? void 0 : _filteredVersions_find.version_number\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 685,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 682,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 660,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 659,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogFooter, {\n                            className: \"flex space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"outline\",\n                                    className: \"flex-1 h-12 text-base\",\n                                    onClick: ()=>setShowConfirmDialog(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 695,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"brand\",\n                                    className: \"flex-1 h-12 text-base\",\n                                    onClick: handleConfirmGeneration,\n                                    children: \"Yes, Generate Videos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 702,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 694,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                    lineNumber: 645,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 644,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n        lineNumber: 193,\n        columnNumber: 5\n    }, this);\n}\n_s(AvatarVideoPanel, \"cQda4cTiyx88ziR/zLX0nTddeMI=\");\n_c = AvatarVideoPanel;\nvar _c;\n$RefreshReg$(_c, \"AvatarVideoPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/AvatarVideoPanel.tsx\n"));

/***/ })

});