"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/avatar-video/page",{

/***/ "(app-pages-browser)/./src/app/admin/AvatarVideoPanel.tsx":
/*!********************************************!*\
  !*** ./src/app/admin/AvatarVideoPanel.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AvatarVideoPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clapperboard.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-user-round.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,CheckCircle,CircleUserRound,Clapperboard,Clock,Play,Sparkles,Users,Video,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction AvatarVideoPanel() {\n    var _avatars_find, _uniqueCourses_find, _filteredVersions_find;\n    _s();\n    const [avatars, setAvatars] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]);\n    const [selectedAvatarId, setSelectedAvatarId] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null);\n    const [approvedVersions, setApprovedVersions] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]);\n    const [selectedCourseIdForVideo, setSelectedCourseIdForVideo] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null);\n    const [selectedVersionId, setSelectedVersionId] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null);\n    const [loadingVersions, setLoadingVersions] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(true);\n    const [loadingAvatars, setLoadingAvatars] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(true);\n    const [generatingVideos, setGeneratingVideos] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false);\n    const [videoGenerationResults, setVideoGenerationResults] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)([]);\n    const [showConfirmDialog, setShowConfirmDialog] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false);\n    const [generationProgress, setGenerationProgress] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)({\n        current: 0,\n        total: 0\n    });\n    const [currentGeneratingPage, setCurrentGeneratingPage] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)('');\n    // Auto-generation states\n    const [autoStartCountdown, setAutoStartCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null);\n    const [hasAutoStarted, setHasAutoStarted] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false);\n    const countdownIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_8__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_8__.useEffect)({\n        \"AvatarVideoPanel.useEffect\": ()=>{\n            const fetchApprovedVersions = {\n                \"AvatarVideoPanel.useEffect.fetchApprovedVersions\": async ()=>{\n                    try {\n                        const res = await fetch(\"http://localhost:5001/api/all-approved-content-versions\");\n                        const data = await res.json();\n                        setApprovedVersions(data);\n                    } catch (err) {\n                        console.error(\"Failed to fetch approved versions:\", err);\n                        alert(\"Failed to load approved content versions.\");\n                    } finally{\n                        setLoadingVersions(false);\n                    }\n                }\n            }[\"AvatarVideoPanel.useEffect.fetchApprovedVersions\"];\n            const fetchAvatars = {\n                \"AvatarVideoPanel.useEffect.fetchAvatars\": async ()=>{\n                    try {\n                        const res = await fetch(\"http://localhost:5001/api/avatars\");\n                        const data = await res.json();\n                        if (Array.isArray(data)) {\n                            setAvatars(data);\n                        } else {\n                            console.error(\"API returned non-array data for avatars:\", data);\n                            setAvatars([]);\n                            alert(\"Failed to load avatar configurations: Invalid data format.\");\n                        }\n                    } catch (err) {\n                        console.error(\"Failed to fetch avatars:\", err);\n                        alert(\"Failed to load avatar configurations. Please ensure backend is running.\");\n                    } finally{\n                        setLoadingAvatars(false);\n                    }\n                }\n            }[\"AvatarVideoPanel.useEffect.fetchAvatars\"];\n            fetchApprovedVersions();\n            fetchAvatars();\n        }\n    }[\"AvatarVideoPanel.useEffect\"], []);\n    // Auto-generation effect - triggers when both avatar and version are selected\n    (0,react__WEBPACK_IMPORTED_MODULE_8__.useEffect)({\n        \"AvatarVideoPanel.useEffect\": ()=>{\n            // Clear any existing countdown\n            if (countdownIntervalRef.current) {\n                clearInterval(countdownIntervalRef.current);\n                countdownIntervalRef.current = null;\n            }\n            // Only auto-start if both selections are made and we haven't already auto-started\n            if (selectedAvatarId && selectedVersionId && !hasAutoStarted && !generatingVideos && videoGenerationResults.length === 0) {\n                console.log(\"Both avatar and version selected, starting auto-generation countdown...\");\n                // Start countdown from 3 seconds\n                setAutoStartCountdown(3);\n                countdownIntervalRef.current = setInterval({\n                    \"AvatarVideoPanel.useEffect\": ()=>{\n                        setAutoStartCountdown({\n                            \"AvatarVideoPanel.useEffect\": (prev)=>{\n                                if (prev === null || prev <= 1) {\n                                    // Countdown finished, start generation\n                                    if (countdownIntervalRef.current) {\n                                        clearInterval(countdownIntervalRef.current);\n                                        countdownIntervalRef.current = null;\n                                    }\n                                    setHasAutoStarted(true);\n                                    setAutoStartCountdown(null);\n                                    // Trigger generation directly without confirmation dialog\n                                    handleConfirmGeneration();\n                                    return null;\n                                }\n                                return prev - 1;\n                            }\n                        }[\"AvatarVideoPanel.useEffect\"]);\n                    }\n                }[\"AvatarVideoPanel.useEffect\"], 1000);\n            } else {\n                setAutoStartCountdown(null);\n            }\n            // Cleanup interval on unmount or dependency change\n            return ({\n                \"AvatarVideoPanel.useEffect\": ()=>{\n                    if (countdownIntervalRef.current) {\n                        clearInterval(countdownIntervalRef.current);\n                        countdownIntervalRef.current = null;\n                    }\n                }\n            })[\"AvatarVideoPanel.useEffect\"];\n        }\n    }[\"AvatarVideoPanel.useEffect\"], [\n        selectedAvatarId,\n        selectedVersionId,\n        hasAutoStarted,\n        generatingVideos,\n        videoGenerationResults.length,\n        handleConfirmGeneration\n    ]);\n    // Reset auto-start flag when selections change\n    (0,react__WEBPACK_IMPORTED_MODULE_8__.useEffect)({\n        \"AvatarVideoPanel.useEffect\": ()=>{\n            if (!selectedAvatarId || !selectedVersionId) {\n                setHasAutoStarted(false);\n                setAutoStartCountdown(null);\n            }\n        }\n    }[\"AvatarVideoPanel.useEffect\"], [\n        selectedAvatarId,\n        selectedVersionId\n    ]);\n    const cancelAutoStart = ()=>{\n        if (countdownIntervalRef.current) {\n            clearInterval(countdownIntervalRef.current);\n            countdownIntervalRef.current = null;\n        }\n        setAutoStartCountdown(null);\n        setHasAutoStarted(true); // Prevent auto-start from happening again\n    };\n    const uniqueCourses = approvedVersions.reduce((acc, version)=>{\n        if (!acc.find((course)=>course.course_id === version.course_id)) {\n            acc.push({\n                course_id: version.course_id,\n                course_title: version.course_title\n            });\n        }\n        return acc;\n    }, []);\n    const filteredVersions = approvedVersions.filter((version)=>version.course_id === Number(selectedCourseIdForVideo));\n    const handleGenerateVideosClick = ()=>{\n        if (!selectedAvatarId || !selectedVersionId) {\n            alert(\"Please select both an avatar and a content version.\");\n            return;\n        }\n        setShowConfirmDialog(true);\n    };\n    const handleConfirmGeneration = async ()=>{\n        setShowConfirmDialog(false);\n        setGeneratingVideos(true);\n        setVideoGenerationResults([]);\n        setGenerationProgress({\n            current: 0,\n            total: 0\n        });\n        setCurrentGeneratingPage('');\n        try {\n            // First get the content to know total pages\n            const versionRes = await fetch(\"http://localhost:5001/api/course-content-versions/\".concat(selectedVersionId));\n            const versionData = await versionRes.json();\n            const totalPages = Array.isArray(versionData.content) ? versionData.content.length : 0;\n            setGenerationProgress({\n                current: 0,\n                total: totalPages\n            });\n            const res = await fetch(\"http://localhost:5001/api/generate-course-videos\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    selectedAvatarId,\n                    selectedVersionId\n                })\n            });\n            const result = await res.json();\n            if (!res.ok) {\n                throw new Error(result.error || \"Failed to initiate video generation\");\n            }\n            console.log(\"Generated Videos Data:\", result.generated_videos);\n            setVideoGenerationResults(result.generated_videos || []);\n        } catch (error) {\n            console.error(\"Video generation error:\", error);\n            alert(\"Video generation failed: \".concat(error.message || \"Unknown error\"));\n        } finally{\n            setGeneratingVideos(false);\n            setCurrentGeneratingPage('');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"mb-8 border-0 shadow-xl bg-white/80 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20 h-20 bg-orange-400 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-10 h-10 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold mb-3 text-gray-900 bg-brand-blue bg-clip-text text-transparent\",\n                                    children: \"Medical Course Avatar & Video Generation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n                                    children: \"Select a medical professional avatar and generate engaging videos from your approved summaries with cutting-edge AI technology\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {\n                            className: \"my-8\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"p-4 transition-all duration-300 \".concat(selectedAvatarId ? 'border-blue-500 bg-blue-50 shadow-lg scale-105' : 'border-gray-200 hover:shadow-md'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-xl flex items-center justify-center transition-colors \".concat(selectedAvatarId ? 'bg-blue-500' : 'bg-gray-200'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-6 h-6 \".concat(selectedAvatarId ? 'text-white' : 'text-gray-400')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold \".concat(selectedAvatarId ? 'text-blue-600' : 'text-gray-500'),\n                                                        children: \"Select Avatar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Choose your presenter\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"p-4 transition-all duration-300 \".concat(selectedVersionId ? 'border-green-500 bg-green-50 shadow-lg scale-105' : 'border-gray-200 hover:shadow-md'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-xl flex items-center justify-center transition-colors \".concat(selectedVersionId ? 'bg-green-500' : 'bg-gray-200'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-6 h-6 \".concat(selectedVersionId ? 'text-white' : 'text-gray-400')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold \".concat(selectedVersionId ? 'text-green-600' : 'text-gray-500'),\n                                                        children: \"Select Content\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Choose course material\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"p-4 transition-all duration-300 \".concat(generatingVideos ? 'border-amber-500 bg-amber-50 shadow-lg scale-105' : 'border-gray-200 hover:shadow-md'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-xl flex items-center justify-center transition-colors \".concat(generatingVideos ? 'bg-amber-500' : 'bg-gray-200'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-6 h-6 \".concat(generatingVideos ? 'text-white' : 'text-gray-400')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold \".concat(generatingVideos ? 'text-amber-600' : 'text-gray-500'),\n                                                        children: \"Generate Videos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Create content\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"mb-8 border-0 shadow-xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        className: \"pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-7 h-7 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"text-2xl text-gray-900\",\n                                            children: \"Select Medical Professional Avatar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mt-1\",\n                                            children: \"Choose your video presenter from our collection of medical professionals\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loadingAvatars ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"Loading avatars...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this) : avatars.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"p-12 text-center bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDC65\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 text-lg\",\n                                    children: \"No avatars available.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                            children: avatars.map((a)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"group relative cursor-pointer transition-all duration-300 hover:shadow-xl \".concat(selectedAvatarId === a.id ? \"border-2 border-blue-500 bg-blue-50 shadow-xl scale-105\" : \"border hover:border-blue-300 hover:bg-blue-50/50 hover:scale-102\"),\n                                    onClick: ()=>setSelectedAvatarId(a.id),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"p-6 text-center\",\n                                        style: {\n                                            minHeight: 280\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center transition-all \".concat(selectedAvatarId === a.id ? 'bg-blue-500 shadow-lg' : 'bg-orange-100 group-hover:bg-blue-100'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-8 h-8 transition-colors \".concat(selectedAvatarId === a.id ? 'text-white' : 'text-orange-600 group-hover:text-blue-600')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold text-gray-900 mb-3 text-lg truncate\",\n                                                title: a.avatar_name,\n                                                children: a.avatar_name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"text-xs\",\n                                                        children: a.specialty || \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500 truncate\",\n                                                        title: a.domain || \"N/A\",\n                                                        children: a.domain || \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-orange-600 truncate\",\n                                                        title: a.voice_id || \"N/A\",\n                                                        children: [\n                                                            \"Voice: \",\n                                                            a.voice_id || \"N/A\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: selectedAvatarId === a.id ? \"default\" : \"outline\",\n                                                className: \"w-full transition-all duration-200 \".concat(selectedAvatarId === a.id ? \"bg-blue-600 hover:bg-blue-700 text-white shadow-md\" : \"hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300\"),\n                                                children: selectedAvatarId === a.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        \"Selected\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 25\n                                                }, this) : \"Select Avatar\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 19\n                                    }, this)\n                                }, a.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this),\n            selectedAvatarId !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"mb-8 border-0 shadow-xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        className: \"pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-7 h-7 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"text-2xl text-gray-900\",\n                                            children: \"Select Course Content for Video Generation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mt-1\",\n                                            children: \"Choose your course and approved version for video creation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loadingVersions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-2 border-gray-300 border-t-green-500 rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"Loading approved content versions...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 15\n                        }, this) : approvedVersions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"p-12 text-center bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDCDA\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 text-lg\",\n                                    children: \"No approved content versions available.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_2__.Label, {\n                                            htmlFor: \"course\",\n                                            className: \"text-base font-semibold text-gray-700\",\n                                            children: \"Course Selection\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                            value: selectedCourseIdForVideo || '',\n                                            onValueChange: (value)=>{\n                                                setSelectedCourseIdForVideo(value);\n                                                setSelectedVersionId(null);\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                                    className: \"w-full h-14 text-base focus:ring-2 focus:ring-green-200 border-2 rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                                        placeholder: \"Choose a course to generate videos from\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                                    children: uniqueCourses.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                            value: course.course_id.toString(),\n                                                            className: \"text-base py-3\",\n                                                            children: course.course_title\n                                                        }, course.course_id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 17\n                                }, this),\n                                selectedCourseIdForVideo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_2__.Label, {\n                                            htmlFor: \"version\",\n                                            className: \"text-base font-semibold text-gray-700\",\n                                            children: \"Version Selection\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.Select, {\n                                            value: selectedVersionId || '',\n                                            onValueChange: (value)=>{\n                                                setSelectedVersionId(value);\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectTrigger, {\n                                                    className: \"w-full h-14 text-base focus:ring-2 focus:ring-green-200 border-2 rounded-xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectValue, {\n                                                        placeholder: \"Select an approved version\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectContent, {\n                                                    children: filteredVersions.map((version)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_3__.SelectItem, {\n                                                            value: version.version_id.toString(),\n                                                            className: \"text-base py-3\",\n                                                            children: [\n                                                                \"Version \",\n                                                                version.version_number,\n                                                                \" (Approved: \",\n                                                                new Date(version.approved_at).toLocaleDateString(),\n                                                                \")\"\n                                                            ]\n                                                        }, version.version_id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 346,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"border-0 shadow-xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        className: \"pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-amber-100 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-7 h-7 text-amber-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"text-2xl text-gray-900\",\n                                                    children: \"Video Generation Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mt-1\",\n                                                    children: \"Generate professional video content from your selections\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: 'warning',\n                                    size: \"lg\",\n                                    className: \"flex items-center space-x-3 px-8 py-4 text-white font-semibold rounded-2xl shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105\",\n                                    onClick: handleGenerateVideosClick,\n                                    disabled: !selectedAvatarId || !selectedVersionId || generatingVideos,\n                                    children: generatingVideos ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"animate-spin w-6 h-6 text-white\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                        className: \"opacity-25\",\n                                                        cx: \"12\",\n                                                        cy: \"12\",\n                                                        r: \"10\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        className: \"opacity-75\",\n                                                        fill: \"currentColor\",\n                                                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: \"Generating Videos...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: selectedAvatarId && selectedVersionId && !hasAutoStarted ? \"Start Now (Skip Auto-Start)\" : \"Generate All Videos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: [\n                            generatingVideos && generationProgress.total > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"mb-8 bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-blue-900 text-lg flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-5 h-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Generation Progress\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"bg-blue-100 text-blue-800 font-semibold\",\n                                                    children: [\n                                                        generationProgress.current,\n                                                        \" of \",\n                                                        generationProgress.total,\n                                                        \" completed\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_6__.Progress, {\n                                            value: generationProgress.current / generationProgress.total * 100,\n                                            className: \"h-3 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 17\n                                        }, this),\n                                        currentGeneratingPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-700 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Currently generating: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium ml-1\",\n                                                    children: currentGeneratingPage\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 43\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 13\n                            }, this),\n                            videoGenerationResults.length === 0 && !generatingVideos && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-32 h-32 bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-16 h-16 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                        children: \"Ready to Generate Videos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 max-w-lg mx-auto mb-8 text-lg leading-relaxed\",\n                                        children: \"Select an avatar and course content to automatically begin creating your professional video content with AI. Generation will start automatically once both selections are made.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 15\n                                    }, this),\n                                    (!selectedAvatarId || !selectedVersionId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-200 max-w-md mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2 text-amber-700 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-lg\",\n                                                            children: \"Selection Required\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-amber-600\",\n                                                    children: \"Please select both an avatar and course content to proceed with video generation.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 17\n                                    }, this),\n                                    selectedAvatarId && selectedVersionId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            autoStartCountdown !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-300 max-w-md mx-auto mb-4 animate-pulse\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center space-x-3 text-blue-700 mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-xl\",\n                                                                            children: autoStartCountdown\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 527,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute inset-0 w-12 h-12 border-4 border-blue-300 rounded-full animate-spin border-t-transparent\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 530,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 526,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-semibold text-lg block\",\n                                                                            children: \"Auto-Starting Generation\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-blue-600\",\n                                                                            children: [\n                                                                                \"Starting in \",\n                                                                                autoStartCountdown,\n                                                                                \" seconds...\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 534,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"w-full border-blue-300 text-blue-700 hover:bg-blue-50\",\n                                                            onClick: cancelAutoStart,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 543,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Cancel Auto-Start\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 21\n                                            }, this),\n                                            autoStartCountdown === null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                className: \"bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200 max-w-md mx-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center space-x-2 text-green-700 mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-6 h-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold text-lg\",\n                                                                    children: hasAutoStarted ? \"Ready to Generate!\" : \"Auto-Generation Ready!\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 556,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-green-600 text-center\",\n                                                            children: hasAutoStarted ? \"Avatar and content selected. Click the button above to start video generation.\" : \"Avatar and content selected. Generation will start automatically in a moment, or click the button above to start immediately.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 13\n                            }, this),\n                            videoGenerationResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-6 h-6 text-gray-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-bold text-gray-800 text-xl\",\n                                                children: \"Generated Videos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 579,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"bg-green-100 text-green-800\",\n                                                children: [\n                                                    videoGenerationResults.length,\n                                                    \" videos processed\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-4\",\n                                        children: videoGenerationResults.map((page, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                                className: \"hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center mb-4\",\n                                                                    children: [\n                                                                        page.generation_status === \"success\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 bg-green-500 rounded-full mr-4 flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-white\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                                lineNumber: 593,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 592,\n                                                                            columnNumber: 31\n                                                                        }, this) : page.generation_status === \"skipped_empty_script\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 bg-yellow-500 rounded-full mr-4 flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-white\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                                lineNumber: 597,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 596,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-5 h-5 bg-red-500 rounded-full mr-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 600,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                            className: \"font-bold text-gray-800 text-xl\",\n                                                                            children: page.title || \"Chapter \".concat(page.page)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 602,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 590,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"mb-4\",\n                                                                    children: [\n                                                                        \"Page \",\n                                                                        page.page\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 606,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                page.generation_status === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: \"bg-green-100 text-green-800 hover:bg-green-100\",\n                                                                    children: \"✅ Generated Successfully\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 611,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                page.generation_status === \"skipped_empty_script\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: \"bg-yellow-100 text-yellow-800 hover:bg-yellow-100\",\n                                                                    children: \"⚠️ Skipped (Empty Script)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 617,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                page.generation_status === \"failed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            className: \"bg-red-100 text-red-800 hover:bg-red-100\",\n                                                                            children: \"❌ Generation Failed\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 624,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-red-600 mt-2 p-3 bg-red-50 rounded-lg border border-red-200\",\n                                                                            children: page.error_details || \"Unknown error occurred during generation\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                            lineNumber: 627,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                                    lineNumber: 623,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 589,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 576,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 424,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: showConfirmDialog,\n                onOpenChange: setShowConfirmDialog,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    className: \"max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20 h-20 bg-gradient-to-br from-amber-400 to-orange-500 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-10 h-10 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                    className: \"text-2xl font-bold text-gray-800 mb-4\",\n                                    children: \"Generate All Videos?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 650,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-6 text-lg leading-relaxed\",\n                                        children: [\n                                            \"This will generate videos for all pages in the selected course content.\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 653,\n                                                columnNumber: 88\n                                            }, this),\n                                            \"This process may take several minutes to complete.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 651,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 646,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-200 mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center font-bold mb-4 text-amber-900 text-lg\",\n                                        children: \"Selected Configuration\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-6 h-6 text-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 665,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-amber-800\",\n                                                            children: \"Avatar\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 666,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-amber-700 break-words\",\n                                                            children: (_avatars_find = avatars.find((a)=>a.id === selectedAvatarId)) === null || _avatars_find === void 0 ? void 0 : _avatars_find.avatar_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 667,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 663,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-6 h-6 text-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 674,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-amber-800\",\n                                                            children: \"Course\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 675,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-amber-700 break-words\",\n                                                            children: (_uniqueCourses_find = uniqueCourses.find((c)=>c.course_id === Number(selectedCourseIdForVideo))) === null || _uniqueCourses_find === void 0 ? void 0 : _uniqueCourses_find.course_title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 676,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_CheckCircle_CircleUserRound_Clapperboard_Clock_Play_Sparkles_Users_Video_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-6 h-6 text-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 683,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-amber-800\",\n                                                            children: \"Version\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 684,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-amber-700 break-words\",\n                                                            children: (_filteredVersions_find = filteredVersions.find((v)=>v.version_id === selectedVersionId)) === null || _filteredVersions_find === void 0 ? void 0 : _filteredVersions_find.version_number\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                            lineNumber: 685,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                    lineNumber: 682,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                lineNumber: 660,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 659,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogFooter, {\n                            className: \"flex space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"outline\",\n                                    className: \"flex-1 h-12 text-base\",\n                                    onClick: ()=>setShowConfirmDialog(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 695,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"brand\",\n                                    className: \"flex-1 h-12 text-base\",\n                                    onClick: handleConfirmGeneration,\n                                    children: \"Yes, Generate Videos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                                    lineNumber: 702,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                            lineNumber: 694,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                    lineNumber: 645,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n                lineNumber: 644,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Chnadrucode\\\\frontend\\\\src\\\\app\\\\admin\\\\AvatarVideoPanel.tsx\",\n        lineNumber: 193,\n        columnNumber: 5\n    }, this);\n}\n_s(AvatarVideoPanel, \"cQda4cTiyx88ziR/zLX0nTddeMI=\");\n_c = AvatarVideoPanel;\nvar _c;\n$RefreshReg$(_c, \"AvatarVideoPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/AvatarVideoPanel.tsx\n"));

/***/ })

});